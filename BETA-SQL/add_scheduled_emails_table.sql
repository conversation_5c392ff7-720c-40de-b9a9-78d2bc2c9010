-- =====================================================
-- SCHEDULED EMAILS TABLE MIGRATION
-- =====================================================
-- 
-- This script creates the scheduled_emails table for the new
-- email scheduling system as described in the system design.
--
-- The scheduled_emails table stores emails that will be sent
-- at specific future dates. The Scheduled Monitor will move
-- due emails to the email_queue (pending_emails) table.
--
-- Author: System Design Implementation
-- Date: 2025-07-29
-- =====================================================

-- Create the scheduled_emails table
CREATE TABLE IF NOT EXISTS scheduled_emails (
    -- Primary key
    id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Email details
    email_address VARCHAR(255) NOT NULL COMMENT 'Recipient email address',
    recipient_name VARCHAR(255) NOT NULL COMMENT 'Recipient name',
    email_type ENUM(
        'welcome',
        'search_engine', 
        'portfolio_builder',
        'platform_complete',
        'reminder',
        'newsletter',
        'announcement',
        'follow_up'
    ) NOT NULL COMMENT 'Type of email to be sent',
    
    -- Email content
    subject VARCHAR(500) NOT NULL COMMENT 'Email subject line',
    content TEXT NOT NULL COMMENT 'Email body content',
    
    -- Scheduling
    scheduled_at TIMESTAMP NOT NULL COMMENT 'When the email should be sent',
    status ENUM(
        'scheduled',    -- Email is scheduled for future delivery
        'queued',       -- Email has been moved to pending_emails queue
        'cancelled',    -- Email has been cancelled
        'expired'       -- Email was not processed and has expired
    ) NOT NULL DEFAULT 'scheduled' COMMENT 'Current status of the scheduled email',
    
    -- Processing tracking
    processed_at TIMESTAMP NULL COMMENT 'When the email was processed (moved to queue or cancelled)',
    queued_email_id INT NULL COMMENT 'Reference to email_queue.id when moved to pending',
    
    -- Retry and error handling
    retry_count INT NOT NULL DEFAULT 0 COMMENT 'Number of processing retry attempts',
    max_retries INT NOT NULL DEFAULT 3 COMMENT 'Maximum number of retry attempts',
    error_message TEXT NULL COMMENT 'Last error message if processing failed',
    
    -- Recurring email support
    is_recurring BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether this is a recurring email',
    recurrence_pattern VARCHAR(100) NULL COMMENT 'Recurrence pattern: daily, weekly, monthly, yearly',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When the scheduled email was created',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'When the record was last updated'
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Primary index for finding due emails (most important query)
CREATE INDEX idx_scheduled_emails_due ON scheduled_emails (scheduled_at, status);

-- Index for filtering by email type and status
CREATE INDEX idx_scheduled_emails_type_status ON scheduled_emails (email_type, status);

-- Index for finding emails by recipient
CREATE INDEX idx_scheduled_emails_email ON scheduled_emails (email_address);

-- Index for status-based queries
CREATE INDEX idx_scheduled_emails_status ON scheduled_emails (status);

-- Index for creation date queries
CREATE INDEX idx_scheduled_emails_created ON scheduled_emails (created_at);

-- Index for processed emails
CREATE INDEX idx_scheduled_emails_processed ON scheduled_emails (processed_at);

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert some sample scheduled emails for testing
-- (These would be the examples from the system design)

INSERT INTO scheduled_emails (
    email_address, 
    recipient_name, 
    email_type, 
    subject, 
    content, 
    scheduled_at,
    status
) VALUES 
(
    '<EMAIL>',
    'User One',
    'reminder',
    'Event reminder...',
    'This is a reminder about the upcoming event. Please make sure to attend.',
    '2025-08-03 09:00:00',
    'scheduled'
),
(
    '<EMAIL>',
    'User Two',
    'newsletter',
    'Weekly update...',
    'Here is your weekly update with the latest news and information.',
    '2025-08-07 10:00:00',
    'scheduled'
),
(
    '<EMAIL>',
    'User Three',
    'follow_up',
    'Final notice...',
    'This is a final notice regarding your account. Please take action soon.',
    '2025-08-10 12:00:00',
    'scheduled'
);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify the table was created successfully
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    CREATE_TIME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'scheduled_emails';

-- Verify indexes were created
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'scheduled_emails'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- Verify sample data was inserted
SELECT 
    id,
    email_address,
    email_type,
    subject,
    scheduled_at,
    status,
    created_at
FROM scheduled_emails
ORDER BY scheduled_at;

-- =====================================================
-- USEFUL QUERIES FOR MONITORING
-- =====================================================

-- Query to find emails due for processing (what the Scheduled Monitor uses)
-- SELECT * FROM scheduled_emails 
-- WHERE status = 'scheduled' 
-- AND scheduled_at <= NOW()
-- ORDER BY scheduled_at ASC;

-- Query to get statistics by status
-- SELECT 
--     status,
--     COUNT(*) as count,
--     MIN(scheduled_at) as earliest,
--     MAX(scheduled_at) as latest
-- FROM scheduled_emails 
-- GROUP BY status;

-- Query to get statistics by email type
-- SELECT 
--     email_type,
--     COUNT(*) as count,
--     COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled_count,
--     COUNT(CASE WHEN status = 'queued' THEN 1 END) as queued_count
-- FROM scheduled_emails 
-- GROUP BY email_type;

-- =====================================================
-- NOTES
-- =====================================================
--
-- 1. The scheduled_emails table works alongside the existing email_queue table
-- 2. The Scheduled Monitor service will query this table every minute
-- 3. Due emails (scheduled_at <= NOW()) will be moved to email_queue
-- 4. The existing email processor will handle emails from email_queue as usual
-- 5. This creates a clean separation between immediate and scheduled emails
--
-- =====================================================
