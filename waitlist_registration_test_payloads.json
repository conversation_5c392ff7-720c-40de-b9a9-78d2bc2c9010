{
  "api_specification": {
    "endpoint": "/auth/signup",
    "method": "POST",
    "base_url": "http://localhost:8000",
    "full_url": "http://localhost:8000/auth/signup",
    "content_type": "application/json",
    "required_headers": {
      "Content-Type": "application/json",
      "Accept": "application/json"
    },
    "optional_headers": {
      "User-Agent": "LawVriksh-Client/1.0",
      "X-Forwarded-For": "client-ip-address"
    },
    "rate_limiting": {
      "limit": 60,
      "window": "per minute",
      "headers_returned": [
        "X-RateLimit-Limit",
        "X-RateLimit-Remaining", 
        "X-RateLimit-Reset",
        "Retry-After"
      ]
    }
  },
  
  "request_schema": {
    "required_fields": {
      "name": {
        "type": "string",
        "min_length": 1,
        "max_length": 100,
        "description": "User's full name"
      },
      "email": {
        "type": "string",
        "format": "email",
        "description": "Valid email address"
      },
      "password": {
        "type": "string", 
        "min_length": 6,
        "max_length": 128,
        "description": "User password"
      }
    },
    "optional_fields": {},
    "validation_rules": {
      "name": "Must be 1-100 characters, non-empty",
      "email": "Must be valid email format",
      "password": "Must be 6-128 characters"
    }
  },

  "response_schemas": {
    "success_response": {
      "status_code": 201,
      "content_type": "application/json",
      "schema": {
        "user_id": "integer",
        "name": "string",
        "email": "string (email format)",
        "created_at": "string (ISO datetime)",
        "total_points": "integer (default: 0)",
        "shares_count": "integer (default: 0)", 
        "default_rank": "integer or null",
        "current_rank": "integer or null",
        "is_admin": "boolean (default: false)"
      }
    },
    "error_responses": {
      "400_bad_request": {
        "status_code": 400,
        "scenarios": [
          "Email already registered",
          "Invalid email format",
          "Name too short/long",
          "Password too short/long",
          "Missing required fields"
        ],
        "schema": {
          "detail": "string (error message)"
        }
      },
      "422_validation_error": {
        "status_code": 422,
        "schema": {
          "detail": [
            {
              "loc": ["array", "field_name"],
              "msg": "string",
              "type": "string"
            }
          ]
        }
      },
      "429_rate_limit": {
        "status_code": 429,
        "schema": {
          "detail": "Rate limit exceeded. Maximum 60 requests per minute allowed."
        }
      },
      "500_server_error": {
        "status_code": 500,
        "schema": {
          "detail": "Internal server error"
        }
      }
    }
  },

  "test_scenarios": {
    "valid_registrations": [
      {
        "name": "Basic Valid Registration",
        "description": "Standard user registration with all required fields",
        "request": {
          "name": "John Doe",
          "email": "<EMAIL>",
          "password": "securepassword123"
        },
        "expected_response": {
          "status_code": 201,
          "response_contains": ["user_id", "name", "email", "created_at"]
        }
      },
      {
        "name": "Registration with Special Characters in Name",
        "description": "User with special characters and accents in name",
        "request": {
          "name": "María José O'Connor-Smith",
          "email": "<EMAIL>",
          "password": "MySecurePass2024!"
        },
        "expected_response": {
          "status_code": 201
        }
      },
      {
        "name": "Registration with Long Valid Name",
        "description": "User with maximum allowed name length",
        "request": {
          "name": "Dr. Alexander Benjamin Christopher Davidson-Wellington III Esq.",
          "email": "<EMAIL>",
          "password": "VeryLongButValidPassword123456"
        },
        "expected_response": {
          "status_code": 201
        }
      },
      {
        "name": "Registration with Minimum Password Length",
        "description": "User with exactly 6 character password",
        "request": {
          "name": "Jane Smith",
          "email": "<EMAIL>",
          "password": "pass12"
        },
        "expected_response": {
          "status_code": 201
        }
      }
    ],

    "invalid_registrations": [
      {
        "name": "Missing Name Field",
        "description": "Registration attempt without name",
        "request": {
          "email": "<EMAIL>",
          "password": "password123"
        },
        "expected_response": {
          "status_code": 422,
          "error_type": "validation_error"
        }
      },
      {
        "name": "Missing Email Field", 
        "description": "Registration attempt without email",
        "request": {
          "name": "No Email User",
          "password": "password123"
        },
        "expected_response": {
          "status_code": 422,
          "error_type": "validation_error"
        }
      },
      {
        "name": "Missing Password Field",
        "description": "Registration attempt without password",
        "request": {
          "name": "No Password User",
          "email": "<EMAIL>"
        },
        "expected_response": {
          "status_code": 422,
          "error_type": "validation_error"
        }
      },
      {
        "name": "Invalid Email Format",
        "description": "Registration with malformed email",
        "request": {
          "name": "Invalid Email User",
          "email": "not-an-email",
          "password": "password123"
        },
        "expected_response": {
          "status_code": 422,
          "error_type": "validation_error"
        }
      },
      {
        "name": "Empty Name Field",
        "description": "Registration with empty name",
        "request": {
          "name": "",
          "email": "<EMAIL>", 
          "password": "password123"
        },
        "expected_response": {
          "status_code": 422,
          "error_type": "validation_error"
        }
      },
      {
        "name": "Password Too Short",
        "description": "Registration with password less than 6 characters",
        "request": {
          "name": "Short Password User",
          "email": "<EMAIL>",
          "password": "12345"
        },
        "expected_response": {
          "status_code": 422,
          "error_type": "validation_error"
        }
      },
      {
        "name": "Name Too Long",
        "description": "Registration with name exceeding 100 characters",
        "request": {
          "name": "This is an extremely long name that exceeds the maximum allowed length of one hundred characters for user names in the system which should cause a validation error",
          "email": "<EMAIL>",
          "password": "password123"
        },
        "expected_response": {
          "status_code": 422,
          "error_type": "validation_error"
        }
      }
    ],

    "duplicate_registration": [
      {
        "name": "Duplicate Email Registration",
        "description": "Attempt to register with already existing email",
        "setup": {
          "description": "First register a user, then try to register again with same email",
          "first_request": {
            "name": "Original User",
            "email": "<EMAIL>",
            "password": "password123"
          }
        },
        "request": {
          "name": "Duplicate User",
          "email": "<EMAIL>", 
          "password": "differentpassword456"
        },
        "expected_response": {
          "status_code": 400,
          "error_message": "Email already registered"
        }
      }
    ],

    "edge_cases": [
      {
        "name": "Unicode Characters in Name",
        "description": "Registration with various Unicode characters",
        "request": {
          "name": "张三 李四 🏛️ ⚖️",
          "email": "<EMAIL>",
          "password": "password123"
        },
        "expected_response": {
          "status_code": 201
        }
      },
      {
        "name": "Email with Plus Sign",
        "description": "Registration with email containing plus sign",
        "request": {
          "name": "Plus Email User",
          "email": "<EMAIL>",
          "password": "password123"
        },
        "expected_response": {
          "status_code": 201
        }
      },
      {
        "name": "Maximum Password Length",
        "description": "Registration with 128 character password",
        "request": {
          "name": "Max Password User",
          "email": "<EMAIL>",
          "password": "a".repeat(128)
        },
        "expected_response": {
          "status_code": 201
        }
      }
    ]
  },

  "curl_examples": {
    "valid_registration": {
      "description": "Example curl command for valid registration",
      "command": "curl -X POST http://localhost:8000/auth/signup \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Accept: application/json\" \\\n  -d '{\n    \"name\": \"John Doe\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"securepassword123\"\n  }'"
    },
    "with_user_agent": {
      "description": "Registration with custom User-Agent header",
      "command": "curl -X POST http://localhost:8000/auth/signup \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Accept: application/json\" \\\n  -H \"User-Agent: LawVriksh-TestClient/1.0\" \\\n  -d '{\n    \"name\": \"Jane Smith\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"mypassword456\"\n  }'"
    },
    "invalid_email_test": {
      "description": "Test invalid email format",
      "command": "curl -X POST http://localhost:8000/auth/signup \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"name\": \"Test User\",\n    \"email\": \"invalid-email\",\n    \"password\": \"password123\"\n  }'"
    }
  },

  "postman_collection": {
    "info": {
      "name": "LawVriksh Waitlist Registration Tests",
      "description": "Comprehensive test collection for user registration API",
      "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
    },
    "variables": [
      {
        "key": "base_url",
        "value": "http://localhost:8000",
        "type": "string"
      }
    ],
    "requests": [
      {
        "name": "Valid Registration",
        "request": {
          "method": "POST",
          "header": [
            {
              "key": "Content-Type",
              "value": "application/json"
            }
          ],
          "body": {
            "mode": "raw",
            "raw": "{\n  \"name\": \"{{$randomFullName}}\",\n  \"email\": \"{{$randomEmail}}\",\n  \"password\": \"{{$randomPassword}}\"\n}"
          },
          "url": {
            "raw": "{{base_url}}/auth/signup",
            "host": ["{{base_url}}"],
            "path": ["auth", "signup"]
          }
        }
      }
    ]
  },

  "load_testing": {
    "description": "Load testing scenarios for registration endpoint",
    "rate_limit_test": {
      "description": "Test rate limiting (60 requests per minute)",
      "scenario": "Send 65 requests within 1 minute to trigger rate limiting",
      "expected_behavior": "First 60 requests succeed, remaining 5 return 429 status"
    },
    "concurrent_registrations": {
      "description": "Test concurrent user registrations",
      "scenario": "Send 10 concurrent registration requests with different emails",
      "expected_behavior": "All 10 should succeed with 201 status"
    }
  },

  "integration_tests": {
    "registration_flow": {
      "description": "Complete registration and verification flow",
      "steps": [
        {
          "step": 1,
          "action": "Register new user",
          "endpoint": "POST /auth/signup",
          "expected": "201 Created with user data"
        },
        {
          "step": 2,
          "action": "Verify welcome email queued",
          "endpoint": "GET /email-queue (admin only)",
          "expected": "Email entry in queue with welcome type"
        },
        {
          "step": 3,
          "action": "Login with new credentials",
          "endpoint": "POST /auth/login",
          "expected": "200 OK with JWT token"
        }
      ]
    }
  },

  "security_tests": {
    "sql_injection": {
      "description": "Test SQL injection attempts",
      "requests": [
        {
          "name": "SQL Injection in Name",
          "request": {
            "name": "'; DROP TABLE users; --",
            "email": "<EMAIL>",
            "password": "password123"
          },
          "expected": "Should be safely handled, no SQL injection"
        },
        {
          "name": "SQL Injection in Email",
          "request": {
            "name": "Hacker User",
            "email": "<EMAIL>'; DROP TABLE users; --",
            "password": "password123"
          },
          "expected": "Should fail validation due to invalid email format"
        }
      ]
    },
    "xss_attempts": {
      "description": "Test XSS prevention",
      "requests": [
        {
          "name": "XSS in Name Field",
          "request": {
            "name": "<script>alert('xss')</script>",
            "email": "<EMAIL>",
            "password": "password123"
          },
          "expected": "Should be safely stored and escaped in responses"
        }
      ]
    }
  },

  "performance_benchmarks": {
    "response_time_targets": {
      "standard_auth": "< 2000ms",
      "async_auth": "< 500ms",
      "ultra_fast_auth": "< 100ms"
    },
    "throughput_targets": {
      "concurrent_users": 50,
      "requests_per_second": 100,
      "success_rate": "> 99%"
    }
  }
}
