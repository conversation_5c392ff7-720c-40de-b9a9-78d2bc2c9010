@echo off
REM =====================================================
REM Scheduled Email Scheduler Startup Script
REM =====================================================
REM
REM This script starts the Scheduled Email Scheduler which
REM monitors the scheduled_emails table and moves due emails
REM to the pending_emails queue for immediate processing.
REM
REM The scheduler implements the "Scheduler Logic" from the
REM system design and runs the Scheduled Monitor every minute.
REM
REM Author: System Design Implementation
REM Date: 2025-07-29
REM =====================================================

echo.
echo =====================================================
echo SCHEDULED EMAIL SCHEDULER
echo =====================================================
echo.
echo Starting the Scheduled Email Scheduler...
echo This will monitor scheduled emails and move due emails to the pending queue.
echo.
echo Press Ctrl+C to stop the scheduler gracefully.
echo.
echo =====================================================
echo.

REM Set the working directory to the backend folder
cd /d "%~dp0"

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
    echo Virtual environment activated.
    echo.
)

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Check if required packages are installed
python -c "import sqlalchemy, fastapi, pydantic" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Required packages are not installed
    echo Please run: pip install -r requirements.txt
    pause
    exit /b 1
)

REM Set environment variables for production
set PYTHONPATH=%CD%
set ENVIRONMENT=production

REM Display configuration
echo Configuration:
echo - Working Directory: %CD%
echo - Python Path: %PYTHONPATH%
echo - Environment: %ENVIRONMENT%
echo.

REM Start the scheduled email scheduler
echo Starting Scheduled Email Scheduler...
echo.

REM Run with default settings (check every 60 seconds, batch size 50)
python scheduled_email_scheduler.py --interval 60 --batch-size 50

REM If the scheduler exits, show the exit code
if errorlevel 1 (
    echo.
    echo =====================================================
    echo SCHEDULER STOPPED WITH ERROR
    echo =====================================================
    echo Exit code: %errorlevel%
    echo Check the logs for more information.
    echo.
) else (
    echo.
    echo =====================================================
    echo SCHEDULER STOPPED GRACEFULLY
    echo =====================================================
    echo The scheduler has been stopped successfully.
    echo.
)

echo Press any key to exit...
pause >nul
