#!/usr/bin/env python3
"""
Scheduled Email System Test
===========================

Comprehensive test script for the new scheduled email system.
Tests all three components of the system design:

1. Ingestion Algorithm (existing email_queue)
2. Immediate Mail Algorithm (existing email processor)  
3. Scheduled Mail Algorithm (new scheduled_emails system)

This script verifies that the system works as described in the design document.
"""

import sys
import os
import time
from datetime import datetime, timedelta
from typing import List, Dict
import pytz

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.scheduled_email import ScheduledEmail, ScheduledEmailType, ScheduledEmailStatus
from app.models.email_queue import EmailQueue, EmailType, EmailStatus
from app.services.scheduled_email_service import create_scheduled_email, get_scheduled_email_stats
from app.services.scheduled_monitor_service import process_due_emails, get_processing_status
from app.schemas.scheduled_email import ScheduledEmailCreate

# Timezone for consistent datetime handling
UTC = pytz.UTC
IST = pytz.timezone('Asia/Kolkata')

def setup_database():
    """Set up database connection."""
    try:
        engine = create_engine(settings.DATABASE_URL, echo=False)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # Test connection
        with SessionLocal() as db:
            db.execute("SELECT 1")
        
        print("✅ Database connection established")
        return SessionLocal
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return None


def test_scheduled_email_creation(db_session):
    """Test creating scheduled emails (Ingestion Algorithm for scheduled emails)."""
    print("\n" + "="*60)
    print("TEST 1: SCHEDULED EMAIL CREATION (INGESTION)")
    print("="*60)
    
    try:
        # Create test scheduled emails with the example dates from the design
        test_emails = [
            {
                "email_address": "<EMAIL>",
                "recipient_name": "User One",
                "email_type": ScheduledEmailType.reminder,
                "subject": "Event reminder...",
                "content": "This is a reminder about the upcoming event.",
                "scheduled_at": datetime(2025, 8, 3, 9, 0, 0, tzinfo=UTC)
            },
            {
                "email_address": "<EMAIL>", 
                "recipient_name": "User Two",
                "email_type": ScheduledEmailType.newsletter,
                "subject": "Weekly update...",
                "content": "Here is your weekly update with the latest news.",
                "scheduled_at": datetime(2025, 8, 7, 10, 0, 0, tzinfo=UTC)
            },
            {
                "email_address": "<EMAIL>",
                "recipient_name": "User Three", 
                "email_type": ScheduledEmailType.follow_up,
                "subject": "Final notice...",
                "content": "This is a final notice regarding your account.",
                "scheduled_at": datetime(2025, 8, 10, 12, 0, 0, tzinfo=UTC)
            },
            # Add one that's due now for testing
            {
                "email_address": "<EMAIL>",
                "recipient_name": "Test User",
                "email_type": ScheduledEmailType.reminder,
                "subject": "Test email due now",
                "content": "This email should be processed immediately.",
                "scheduled_at": datetime.now(UTC) - timedelta(minutes=1)  # 1 minute ago
            }
        ]
        
        created_emails = []
        
        with db_session() as db:
            for email_data in test_emails:
                email_create = ScheduledEmailCreate(**email_data)
                
                # Skip validation for past dates in test
                if email_data["scheduled_at"] < datetime.now(UTC):
                    # Create directly without validation
                    scheduled_email = ScheduledEmail(
                        email_address=email_data["email_address"],
                        recipient_name=email_data["recipient_name"],
                        email_type=email_data["email_type"],
                        subject=email_data["subject"],
                        content=email_data["content"],
                        scheduled_at=email_data["scheduled_at"],
                        status=ScheduledEmailStatus.scheduled
                    )
                    db.add(scheduled_email)
                    db.commit()
                    db.refresh(scheduled_email)
                    created_emails.append(scheduled_email)
                else:
                    scheduled_email = create_scheduled_email(db, email_create)
                    created_emails.append(scheduled_email)
                
                print(f"✅ Created scheduled email: {email_data['subject']} for {email_data['email_address']}")
                print(f"   Scheduled at: {email_data['scheduled_at']}")
                print(f"   Status: {scheduled_email.status}")
                print(f"   ID: {scheduled_email.id}")
        
        print(f"\n✅ Successfully created {len(created_emails)} scheduled emails")
        return created_emails
        
    except Exception as e:
        print(f"❌ Scheduled email creation failed: {e}")
        return []


def test_scheduled_email_stats(db_session):
    """Test getting scheduled email statistics."""
    print("\n" + "="*60)
    print("TEST 2: SCHEDULED EMAIL STATISTICS")
    print("="*60)
    
    try:
        with db_session() as db:
            stats = get_scheduled_email_stats(db)
            
            print(f"📊 Scheduled Email Statistics:")
            print(f"   Total scheduled: {stats.total_scheduled}")
            print(f"   Total queued: {stats.total_queued}")
            print(f"   Total cancelled: {stats.total_cancelled}")
            print(f"   Total expired: {stats.total_expired}")
            print(f"   Due now: {stats.due_now}")
            print(f"   Due within hour: {stats.due_within_hour}")
            print(f"   Due within day: {stats.due_within_day}")
            
            print(f"\n📈 By Email Type:")
            for email_type, count in stats.by_type.items():
                print(f"   {email_type.value}: {count}")
        
        print(f"\n✅ Successfully retrieved scheduled email statistics")
        return True
        
    except Exception as e:
        print(f"❌ Statistics retrieval failed: {e}")
        return False


def test_scheduled_monitor_processing(db_session):
    """Test the Scheduled Monitor (Watchman 2) processing due emails."""
    print("\n" + "="*60)
    print("TEST 3: SCHEDULED MONITOR PROCESSING (WATCHMAN 2)")
    print("="*60)
    
    try:
        with db_session() as db:
            # Get processing status before
            status_before = get_processing_status(db)
            print(f"📋 Status before processing:")
            print(f"   Due now: {status_before['due_now']}")
            print(f"   Total scheduled: {status_before['total_scheduled']}")
            print(f"   Total queued: {status_before['total_queued']}")
            
            if status_before['due_now'] == 0:
                print("ℹ️  No emails due for processing")
                return True
            
            # Process due emails
            print(f"\n🔄 Processing due emails...")
            result = process_due_emails(db, batch_size=10)
            
            print(f"📊 Processing Results:")
            print(f"   Processed: {result.processed_count}")
            print(f"   Queued: {result.queued_count}")
            print(f"   Failed: {result.failed_count}")
            print(f"   Processing time: {result.processing_time_seconds:.2f}s")
            
            if result.errors:
                print(f"⚠️  Errors:")
                for error in result.errors:
                    print(f"   - {error}")
            
            # Get processing status after
            status_after = get_processing_status(db)
            print(f"\n📋 Status after processing:")
            print(f"   Due now: {status_after['due_now']}")
            print(f"   Total scheduled: {status_after['total_scheduled']}")
            print(f"   Total queued: {status_after['total_queued']}")
            
            # Verify emails were moved to pending queue
            pending_emails = db.query(EmailQueue).filter(
                EmailQueue.status == EmailStatus.pending
            ).all()
            
            print(f"\n📧 Pending emails in queue: {len(pending_emails)}")
            for email in pending_emails[-3:]:  # Show last 3
                print(f"   - {email.subject} for {email.user_email}")
        
        print(f"\n✅ Scheduled monitor processing completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Scheduled monitor processing failed: {e}")
        return False


def test_integration_flow(db_session):
    """Test the complete integration flow from scheduled to pending to sent."""
    print("\n" + "="*60)
    print("TEST 4: COMPLETE INTEGRATION FLOW")
    print("="*60)
    
    try:
        with db_session() as db:
            # Create a scheduled email due now
            test_email_data = ScheduledEmailCreate(
                email_address="<EMAIL>",
                recipient_name="Integration Test",
                email_type=ScheduledEmailType.reminder,
                subject="Integration test email",
                content="This email tests the complete flow from scheduled to sent.",
                scheduled_at=datetime.now(UTC) - timedelta(seconds=30)  # 30 seconds ago
            )
            
            # Create directly without validation for past date
            scheduled_email = ScheduledEmail(
                email_address=test_email_data.email_address,
                recipient_name=test_email_data.recipient_name,
                email_type=test_email_data.email_type,
                subject=test_email_data.subject,
                content=test_email_data.content,
                scheduled_at=test_email_data.scheduled_at,
                status=ScheduledEmailStatus.scheduled
            )
            db.add(scheduled_email)
            db.commit()
            db.refresh(scheduled_email)
            
            print(f"✅ Step 1: Created scheduled email (ID: {scheduled_email.id})")
            
            # Process the due email
            result = process_due_emails(db, batch_size=1)
            
            print(f"✅ Step 2: Processed due email")
            print(f"   Queued: {result.queued_count}")
            
            # Verify the email was moved to pending queue
            db.refresh(scheduled_email)
            print(f"✅ Step 3: Scheduled email status: {scheduled_email.status}")
            
            if scheduled_email.queued_email_id:
                queued_email = db.query(EmailQueue).filter(
                    EmailQueue.id == scheduled_email.queued_email_id
                ).first()
                
                if queued_email:
                    print(f"✅ Step 4: Found corresponding queue entry (ID: {queued_email.id})")
                    print(f"   Queue status: {queued_email.status}")
                    print(f"   Subject: {queued_email.subject}")
                else:
                    print(f"❌ Step 4: Queue entry not found")
            
        print(f"\n✅ Complete integration flow test passed")
        return True
        
    except Exception as e:
        print(f"❌ Integration flow test failed: {e}")
        return False


def cleanup_test_data(db_session):
    """Clean up test data."""
    print("\n" + "="*60)
    print("CLEANUP: REMOVING TEST DATA")
    print("="*60)
    
    try:
        with db_session() as db:
            # Remove test scheduled emails
            test_emails = db.query(ScheduledEmail).filter(
                ScheduledEmail.email_address.in_([
                    "<EMAIL>",
                    "<EMAIL>", 
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ])
            ).all()
            
            for email in test_emails:
                db.delete(email)
            
            # Remove test queue entries
            test_queue_emails = db.query(EmailQueue).filter(
                EmailQueue.user_email.in_([
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>", 
                    "<EMAIL>",
                    "<EMAIL>"
                ])
            ).all()
            
            for email in test_queue_emails:
                db.delete(email)
            
            db.commit()
            
            print(f"✅ Removed {len(test_emails)} scheduled emails")
            print(f"✅ Removed {len(test_queue_emails)} queue emails")
        
        return True
        
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        return False


def main():
    """Run all tests."""
    print("="*80)
    print("SCHEDULED EMAIL SYSTEM COMPREHENSIVE TEST")
    print("="*80)
    print("Testing the complete email scheduling system as described in the design:")
    print("1. Ingestion Algorithm (scheduled_emails table)")
    print("2. Immediate Mail Algorithm (existing email_queue processing)")
    print("3. Scheduled Mail Algorithm (Scheduled Monitor)")
    print("="*80)
    
    # Setup database
    db_session = setup_database()
    if not db_session:
        print("❌ Cannot proceed without database connection")
        return False
    
    # Run tests
    test_results = []
    
    # Test 1: Scheduled email creation
    result1 = test_scheduled_email_creation(db_session)
    test_results.append(("Scheduled Email Creation", len(result1) > 0))
    
    # Test 2: Statistics
    result2 = test_scheduled_email_stats(db_session)
    test_results.append(("Scheduled Email Statistics", result2))
    
    # Test 3: Scheduled monitor processing
    result3 = test_scheduled_monitor_processing(db_session)
    test_results.append(("Scheduled Monitor Processing", result3))
    
    # Test 4: Integration flow
    result4 = test_integration_flow(db_session)
    test_results.append(("Complete Integration Flow", result4))
    
    # Cleanup
    cleanup_result = cleanup_test_data(db_session)
    test_results.append(("Cleanup", cleanup_result))
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<40} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The scheduled email system is working correctly.")
        print("\nNext steps:")
        print("1. Run the SQL migration: BETA-SQL/add_scheduled_emails_table.sql")
        print("2. Start the scheduler: run_scheduled_email_scheduler.bat")
        print("3. Use the API endpoints to manage scheduled emails")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    print("="*80)
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
