@echo off
REM =====================================================
REM Complete Email System Startup Script
REM =====================================================
REM
REM This script starts both the main FastAPI application
REM and the Scheduled Email Scheduler to run the complete
REM email system with both immediate and scheduled emails.
REM
REM Components started:
REM 1. FastAPI Application (main server)
REM 2. Scheduled Email Scheduler (background process)
REM
REM Author: System Design Implementation
REM Date: 2025-07-29
REM =====================================================

echo.
echo =====================================================
echo COMPLETE EMAIL SYSTEM STARTUP
echo =====================================================
echo.
echo Starting the complete email system with:
echo 1. Main FastAPI application
echo 2. Scheduled Email Scheduler
echo.
echo This will handle both immediate and scheduled emails.
echo.
echo Press Ctrl+C to stop all services.
echo.
echo =====================================================
echo.

REM Set the working directory to the backend folder
cd /d "%~dp0"

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
    echo Virtual environment activated.
    echo.
)

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Check if required packages are installed
python -c "import sqlalchemy, fastapi, pydantic, uvicorn" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Required packages are not installed
    echo Please run: pip install -r requirements.txt
    pause
    exit /b 1
)

REM Set environment variables
set PYTHONPATH=%CD%
set ENVIRONMENT=production

REM Display configuration
echo Configuration:
echo - Working Directory: %CD%
echo - Python Path: %PYTHONPATH%
echo - Environment: %ENVIRONMENT%
echo.

REM Create log directory if it doesn't exist
if not exist "logs" mkdir logs

echo Starting services...
echo.

REM Start the scheduled email scheduler in background
echo [1/2] Starting Scheduled Email Scheduler...
start "Scheduled Email Scheduler" /min python scheduled_email_scheduler.py --interval 60 --batch-size 50

REM Wait a moment for scheduler to start
timeout /t 3 /nobreak >nul

echo [2/2] Starting FastAPI Application...
echo.
echo =====================================================
echo SYSTEM READY
echo =====================================================
echo.
echo Services running:
echo - FastAPI Application: http://localhost:8000
echo - API Documentation: http://localhost:8000/docs
echo - Scheduled Email Scheduler: Background process
echo.
echo API Endpoints for Scheduled Emails:
echo - POST   /scheduled-emails/              (Create)
echo - GET    /scheduled-emails/              (List)
echo - GET    /scheduled-emails/stats/overview (Statistics)
echo - POST   /scheduled-emails/process/due    (Manual trigger)
echo.
echo Press Ctrl+C to stop all services.
echo =====================================================
echo.

REM Start the main FastAPI application (this will block)
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

REM If we reach here, the main app has stopped
echo.
echo =====================================================
echo SHUTTING DOWN SERVICES
echo =====================================================
echo.

REM Try to gracefully stop the scheduler
echo Stopping Scheduled Email Scheduler...
taskkill /f /fi "WindowTitle eq Scheduled Email Scheduler*" >nul 2>&1

echo All services stopped.
echo.
echo Press any key to exit...
pause >nul
