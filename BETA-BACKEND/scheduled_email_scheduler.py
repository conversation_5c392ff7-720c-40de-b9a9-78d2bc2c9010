#!/usr/bin/env python3
"""
Scheduled Email Scheduler
========================

This is the main scheduler component that implements the "Scheduler Logic" 
from the system design. It runs continuously and triggers the Scheduled Monitor
to check for due emails.

The scheduler runs at a set interval (every minute) and executes the 
Scheduled Monitor's process_due_emails() function.

Usage:
    python scheduled_email_scheduler.py [--interval SECONDS] [--batch-size SIZE] [--dry-run]

Example:
    python scheduled_email_scheduler.py --interval 60 --batch-size 50
"""

import sys
import os
import time
import signal
import argparse
import logging
from datetime import datetime
from typing import Optional
import pytz

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.services.scheduled_monitor_service import process_due_emails, get_processing_status

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduled_email_scheduler.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Global flag for graceful shutdown
shutdown_requested = False


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    global shutdown_requested
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    shutdown_requested = True


def setup_database():
    """Set up database connection."""
    try:
        # Create database engine
        engine = create_engine(
            settings.DATABASE_URL,
            pool_pre_ping=True,
            pool_recycle=300,
            echo=False
        )
        
        # Create session factory
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # Test connection
        with SessionLocal() as db:
            db.execute("SELECT 1")
        
        logger.info("Database connection established successfully")
        return SessionLocal
        
    except Exception as e:
        logger.error(f"Failed to setup database: {e}")
        return None


def run_scheduled_monitor_cycle(session_factory, batch_size: int = 50, dry_run: bool = False):
    """
    Run one cycle of the Scheduled Monitor.
    
    Args:
        session_factory: Database session factory
        batch_size: Maximum number of emails to process per cycle
        dry_run: If True, don't actually move emails to queue
        
    Returns:
        dict: Processing results
    """
    try:
        with session_factory() as db:
            # Get current status before processing
            status_before = get_processing_status(db)
            
            logger.info(
                f"Starting scheduled monitor cycle - "
                f"Due now: {status_before['due_now']}, "
                f"Total scheduled: {status_before['total_scheduled']}"
            )
            
            if status_before['due_now'] == 0:
                logger.info("No due emails found, skipping processing")
                return {
                    'processed_count': 0,
                    'queued_count': 0,
                    'failed_count': 0,
                    'errors': [],
                    'processing_time_seconds': 0.0
                }
            
            # Process due emails
            if dry_run:
                logger.info("DRY RUN MODE - No emails will actually be moved to queue")
            
            result = process_due_emails(db, batch_size=batch_size)
            
            # Get status after processing
            status_after = get_processing_status(db)
            
            logger.info(
                f"Scheduled monitor cycle completed - "
                f"Processed: {result.processed_count}, "
                f"Queued: {result.queued_count}, "
                f"Failed: {result.failed_count}, "
                f"Time: {result.processing_time_seconds:.2f}s"
            )
            
            if result.errors:
                logger.warning(f"Errors encountered: {result.errors}")
            
            # Log next due email info
            if status_after['next_due_at']:
                logger.info(f"Next email due at: {status_after['next_due_at']}")
            else:
                logger.info("No more scheduled emails")
            
            return result.dict()
            
    except Exception as e:
        logger.error(f"Error in scheduled monitor cycle: {e}")
        return {
            'processed_count': 0,
            'queued_count': 0,
            'failed_count': 0,
            'errors': [str(e)],
            'processing_time_seconds': 0.0
        }


def main():
    """Main scheduler loop."""
    parser = argparse.ArgumentParser(description='Scheduled Email Scheduler')
    parser.add_argument(
        '--interval', 
        type=int, 
        default=60, 
        help='Check interval in seconds (default: 60)'
    )
    parser.add_argument(
        '--batch-size', 
        type=int, 
        default=50, 
        help='Maximum emails to process per cycle (default: 50)'
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true', 
        help='Run in dry-run mode (don\'t actually move emails)'
    )
    parser.add_argument(
        '--max-cycles', 
        type=int, 
        help='Maximum number of cycles to run (for testing)'
    )
    
    args = parser.parse_args()
    
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("=" * 60)
    logger.info("SCHEDULED EMAIL SCHEDULER STARTING")
    logger.info("=" * 60)
    logger.info(f"Check interval: {args.interval} seconds")
    logger.info(f"Batch size: {args.batch_size}")
    logger.info(f"Dry run mode: {args.dry_run}")
    if args.max_cycles:
        logger.info(f"Max cycles: {args.max_cycles}")
    logger.info("=" * 60)
    
    # Setup database
    session_factory = setup_database()
    if not session_factory:
        logger.error("Failed to setup database, exiting")
        sys.exit(1)
    
    # Main scheduler loop
    cycle_count = 0
    total_processed = 0
    total_queued = 0
    total_failed = 0
    
    try:
        while not shutdown_requested:
            cycle_count += 1
            cycle_start_time = datetime.now()
            
            logger.info(f"Starting cycle {cycle_count} at {cycle_start_time}")
            
            # Run scheduled monitor cycle
            result = run_scheduled_monitor_cycle(
                session_factory, 
                batch_size=args.batch_size,
                dry_run=args.dry_run
            )
            
            # Update totals
            total_processed += result['processed_count']
            total_queued += result['queued_count']
            total_failed += result['failed_count']
            
            cycle_end_time = datetime.now()
            cycle_duration = (cycle_end_time - cycle_start_time).total_seconds()
            
            logger.info(
                f"Cycle {cycle_count} completed in {cycle_duration:.2f}s - "
                f"Total stats: Processed={total_processed}, "
                f"Queued={total_queued}, Failed={total_failed}"
            )
            
            # Check if we should stop (for testing)
            if args.max_cycles and cycle_count >= args.max_cycles:
                logger.info(f"Reached maximum cycles ({args.max_cycles}), stopping")
                break
            
            # Wait for next cycle
            if not shutdown_requested:
                logger.info(f"Waiting {args.interval} seconds until next cycle...")
                
                # Sleep in small intervals to allow for responsive shutdown
                sleep_remaining = args.interval
                while sleep_remaining > 0 and not shutdown_requested:
                    sleep_time = min(1, sleep_remaining)
                    time.sleep(sleep_time)
                    sleep_remaining -= sleep_time
    
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
    except Exception as e:
        logger.error(f"Unexpected error in main loop: {e}")
    
    finally:
        logger.info("=" * 60)
        logger.info("SCHEDULED EMAIL SCHEDULER STOPPING")
        logger.info("=" * 60)
        logger.info(f"Total cycles: {cycle_count}")
        logger.info(f"Total emails processed: {total_processed}")
        logger.info(f"Total emails queued: {total_queued}")
        logger.info(f"Total failures: {total_failed}")
        logger.info("Scheduler stopped gracefully")
        logger.info("=" * 60)


if __name__ == "__main__":
    main()
