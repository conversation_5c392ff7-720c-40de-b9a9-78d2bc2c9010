"""
Scheduled Email Model for Future Email Delivery
==============================================

This model handles emails scheduled for specific future dates.
Works alongside the existing email_queue (pending_emails) table.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, Index, Boolean
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class ScheduledEmailType(str, enum.Enum):
    """Email types supported by the scheduled email system."""
    welcome = "welcome"
    search_engine = "search_engine"
    portfolio_builder = "portfolio_builder"
    platform_complete = "platform_complete"
    reminder = "reminder"
    newsletter = "newsletter"
    announcement = "announcement"
    follow_up = "follow_up"


class ScheduledEmailStatus(str, enum.Enum):
    """Scheduled email processing status."""
    scheduled = "scheduled"  # Email is scheduled for future delivery
    queued = "queued"       # Email has been moved to pending_emails queue
    cancelled = "cancelled" # Email has been cancelled
    expired = "expired"     # Email was not processed and has expired


class ScheduledEmail(Base):
    """
    Scheduled Email Model
    
    Stores emails to be sent at specific future dates.
    The Scheduled Monitor will move due emails to the pending_emails queue.
    """
    __tablename__ = "scheduled_emails"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Email details
    email_address = Column(String(255), nullable=False, index=True)
    recipient_name = Column(String(255), nullable=False)
    email_type = Column(
        Enum(ScheduledEmailType), 
        nullable=False, 
        index=True
    )
    
    # Email content
    subject = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    
    # Scheduling
    scheduled_at = Column(DateTime(timezone=True), nullable=False, index=True)
    status = Column(
        Enum(ScheduledEmailStatus),
        nullable=False,
        default=ScheduledEmailStatus.scheduled,
        index=True
    )
    
    # Processing tracking
    processed_at = Column(DateTime(timezone=True), nullable=True)
    queued_email_id = Column(Integer, nullable=True)  # Reference to email_queue.id when moved
    
    # Retry and error handling
    retry_count = Column(Integer, nullable=False, default=0)
    max_retries = Column(Integer, nullable=False, default=3)
    error_message = Column(Text, nullable=True)
    
    # Additional options
    is_recurring = Column(Boolean, nullable=False, default=False)
    recurrence_pattern = Column(String(100), nullable=True)  # e.g., "weekly", "monthly"
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        onupdate=func.now()
    )
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_scheduled_emails_due', 'scheduled_at', 'status'),
        Index('idx_scheduled_emails_type_status', 'email_type', 'status'),
        Index('idx_scheduled_emails_created', 'created_at'),
    )
    
    def __repr__(self):
        return f"<ScheduledEmail(id={self.id}, email={self.email_address}, type={self.email_type}, scheduled_at={self.scheduled_at}, status={self.status})>"
    
    @property
    def is_scheduled(self) -> bool:
        """Check if email is scheduled."""
        return self.status == ScheduledEmailStatus.scheduled
    
    @property
    def is_queued(self) -> bool:
        """Check if email has been queued."""
        return self.status == ScheduledEmailStatus.queued
    
    @property
    def is_cancelled(self) -> bool:
        """Check if email has been cancelled."""
        return self.status == ScheduledEmailStatus.cancelled
    
    @property
    def is_expired(self) -> bool:
        """Check if email has expired."""
        return self.status == ScheduledEmailStatus.expired
    
    @property
    def can_be_processed(self) -> bool:
        """Check if email can be processed (moved to queue)."""
        return self.status == ScheduledEmailStatus.scheduled
    
    @property
    def is_due(self) -> bool:
        """Check if email is due for processing."""
        from datetime import datetime
        import pytz
        
        if not self.can_be_processed:
            return False
            
        current_time = datetime.now(pytz.UTC)
        
        # Handle timezone-aware comparison
        scheduled_time = self.scheduled_at
        if scheduled_time.tzinfo is None:
            # If scheduled_at is naive, assume UTC
            scheduled_time = pytz.UTC.localize(scheduled_time)
        
        return scheduled_time <= current_time
    
    @property
    def time_until_due(self) -> int:
        """Get seconds until email is due. Returns 0 if already due."""
        from datetime import datetime
        import pytz
        
        if not self.can_be_processed:
            return -1
            
        current_time = datetime.now(pytz.UTC)
        
        # Handle timezone-aware comparison
        scheduled_time = self.scheduled_at
        if scheduled_time.tzinfo is None:
            # If scheduled_at is naive, assume UTC
            scheduled_time = pytz.UTC.localize(scheduled_time)
        
        time_diff = (scheduled_time - current_time).total_seconds()
        return max(0, int(time_diff))
