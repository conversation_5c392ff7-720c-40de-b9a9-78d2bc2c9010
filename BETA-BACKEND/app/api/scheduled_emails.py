"""
Scheduled Emails API
===================

API endpoints for managing scheduled emails.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from app.core.dependencies import get_db
from app.models.scheduled_email import ScheduledEmailType, ScheduledEmailStatus
from app.schemas.scheduled_email import (
    ScheduledEmailCreate, ScheduledEmailUpdate, ScheduledEmailResponse,
    ScheduledEmailStats, ScheduledEmailBatch, ScheduledEmailProcessingResult,
    ScheduledEmailListResponse
)
from app.services.scheduled_email_service import (
    create_scheduled_email, get_scheduled_email, get_scheduled_emails,
    update_scheduled_email, cancel_scheduled_email, delete_scheduled_email,
    get_scheduled_email_stats, get_due_scheduled_emails
)
from app.services.scheduled_monitor_service import process_due_emails, get_processing_status

router = APIRouter(prefix="/scheduled-emails", tags=["Scheduled Emails"])


@router.post("/", response_model=ScheduledEmailResponse, status_code=status.HTTP_201_CREATED)
def create_scheduled_email_endpoint(
    email_data: ScheduledEmailCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new scheduled email.
    
    The email will be automatically moved to the pending queue when its scheduled time arrives.
    """
    try:
        scheduled_email = create_scheduled_email(db, email_data)
        return scheduled_email
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create scheduled email: {str(e)}"
        )


@router.get("/", response_model=ScheduledEmailListResponse)
def list_scheduled_emails(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of records to return"),
    status: Optional[ScheduledEmailStatus] = Query(None, description="Filter by status"),
    email_type: Optional[ScheduledEmailType] = Query(None, description="Filter by email type"),
    email_address: Optional[str] = Query(None, description="Filter by email address"),
    due_before: Optional[datetime] = Query(None, description="Filter emails due before this datetime"),
    db: Session = Depends(get_db)
):
    """
    List scheduled emails with optional filtering and pagination.
    """
    emails = get_scheduled_emails(
        db=db,
        skip=skip,
        limit=limit,
        status=status,
        email_type=email_type,
        email_address=email_address,
        due_before=due_before
    )
    
    # Get total count for pagination
    total = len(get_scheduled_emails(
        db=db,
        skip=0,
        limit=10000,  # Large number to get total count
        status=status,
        email_type=email_type,
        email_address=email_address,
        due_before=due_before
    ))
    
    total_pages = (total + limit - 1) // limit
    current_page = (skip // limit) + 1
    
    return ScheduledEmailListResponse(
        emails=emails,
        total=total,
        page=current_page,
        per_page=limit,
        total_pages=total_pages,
        has_next=current_page < total_pages,
        has_prev=current_page > 1
    )


@router.get("/{email_id}", response_model=ScheduledEmailResponse)
def get_scheduled_email_endpoint(
    email_id: int,
    db: Session = Depends(get_db)
):
    """
    Get a specific scheduled email by ID.
    """
    scheduled_email = get_scheduled_email(db, email_id)
    if not scheduled_email:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scheduled email not found"
        )
    return scheduled_email


@router.put("/{email_id}", response_model=ScheduledEmailResponse)
def update_scheduled_email_endpoint(
    email_id: int,
    email_data: ScheduledEmailUpdate,
    db: Session = Depends(get_db)
):
    """
    Update a scheduled email.
    
    Only emails with status 'scheduled' can be updated.
    """
    scheduled_email = update_scheduled_email(db, email_id, email_data)
    if not scheduled_email:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scheduled email not found"
        )
    return scheduled_email


@router.delete("/{email_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_scheduled_email_endpoint(
    email_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete a scheduled email permanently.
    """
    success = delete_scheduled_email(db, email_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scheduled email not found"
        )


@router.post("/{email_id}/cancel", response_model=ScheduledEmailResponse)
def cancel_scheduled_email_endpoint(
    email_id: int,
    db: Session = Depends(get_db)
):
    """
    Cancel a scheduled email.
    
    Only emails with status 'scheduled' can be cancelled.
    """
    success = cancel_scheduled_email(db, email_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scheduled email not found or cannot be cancelled"
        )
    
    # Return updated email
    return get_scheduled_email(db, email_id)


@router.get("/stats/overview", response_model=ScheduledEmailStats)
def get_scheduled_email_stats_endpoint(db: Session = Depends(get_db)):
    """
    Get statistics about scheduled emails.
    """
    return get_scheduled_email_stats(db)


@router.get("/due/list", response_model=List[ScheduledEmailResponse])
def list_due_scheduled_emails(
    limit: int = Query(50, ge=1, le=100, description="Maximum number of emails to return"),
    db: Session = Depends(get_db)
):
    """
    List scheduled emails that are due for processing.
    """
    return get_due_scheduled_emails(db, limit=limit)


@router.post("/process/due", response_model=ScheduledEmailProcessingResult)
def process_due_emails_endpoint(
    batch_size: int = Query(50, ge=1, le=100, description="Maximum number of emails to process"),
    db: Session = Depends(get_db)
):
    """
    Manually trigger processing of due scheduled emails.
    
    This moves due emails from scheduled_emails to the pending queue (email_queue).
    Normally this is done automatically by the scheduler, but this endpoint allows manual triggering.
    """
    return process_due_emails(db, batch_size=batch_size)


@router.get("/status/processing", response_model=dict)
def get_processing_status_endpoint(db: Session = Depends(get_db)):
    """
    Get current status of scheduled email processing.
    """
    return get_processing_status(db)


@router.post("/batch", response_model=dict)
def batch_operations(
    batch_data: ScheduledEmailBatch,
    db: Session = Depends(get_db)
):
    """
    Perform batch operations on scheduled emails.
    
    Supported actions:
    - cancel: Cancel multiple scheduled emails
    - reschedule: Reschedule multiple emails to a new date
    - queue: Manually queue multiple emails (move to pending)
    """
    results = {
        'success_count': 0,
        'failed_count': 0,
        'errors': []
    }
    
    for email_id in batch_data.email_ids:
        try:
            if batch_data.action == 'cancel':
                success = cancel_scheduled_email(db, email_id)
                if success:
                    results['success_count'] += 1
                else:
                    results['failed_count'] += 1
                    results['errors'].append(f"Failed to cancel email {email_id}")
            
            elif batch_data.action == 'reschedule':
                if not batch_data.new_scheduled_at:
                    results['failed_count'] += 1
                    results['errors'].append(f"No new_scheduled_at provided for email {email_id}")
                    continue
                
                update_data = ScheduledEmailUpdate(scheduled_at=batch_data.new_scheduled_at)
                updated_email = update_scheduled_email(db, email_id, update_data)
                if updated_email:
                    results['success_count'] += 1
                else:
                    results['failed_count'] += 1
                    results['errors'].append(f"Failed to reschedule email {email_id}")
            
            elif batch_data.action == 'queue':
                # This would manually queue the email (implementation depends on requirements)
                results['failed_count'] += 1
                results['errors'].append(f"Manual queueing not implemented for email {email_id}")
            
        except Exception as e:
            results['failed_count'] += 1
            results['errors'].append(f"Error processing email {email_id}: {str(e)}")
    
    return results
