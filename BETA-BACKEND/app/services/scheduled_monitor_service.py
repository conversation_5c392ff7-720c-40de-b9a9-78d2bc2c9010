"""
Scheduled Monitor Service (Watchman 2)
=====================================

This service monitors the scheduled_emails table and moves due emails 
to the pending_emails (email_queue) table for immediate processing.

This is the core component that implements the "Scheduled Mail Algorithm"
described in the system design.
"""

from sqlalchemy.orm import Session
from datetime import datetime
from typing import List, Tuple
import logging
import pytz

from app.models.scheduled_email import ScheduledEmail, ScheduledEmailStatus
from app.models.email_queue import EmailQueue, EmailStatus, EmailType
from app.services.scheduled_email_service import (
    get_due_scheduled_emails, mark_scheduled_email_as_queued
)
from app.schemas.scheduled_email import ScheduledEmailProcessingResult

logger = logging.getLogger(__name__)

# Timezone for consistent datetime handling
UTC = pytz.UTC


def process_due_emails(db: Session, batch_size: int = 50) -> ScheduledEmailProcessingResult:
    """
    Main function: Process due scheduled emails by moving them to the pending queue.
    
    This function implements the core logic described in the system design:
    1. Find Due Emails: Query scheduled_emails where scheduled_at <= now and status = 'scheduled'
    2. Move to Queue: Insert into email_queue (pending_emails) and update status to 'queued'
    
    Args:
        db: Database session
        batch_size: Maximum number of emails to process in one batch
        
    Returns:
        ScheduledEmailProcessingResult: Processing statistics
    """
    start_time = datetime.now()
    processed_count = 0
    queued_count = 0
    failed_count = 0
    errors = []
    
    try:
        logger.info(f"Starting scheduled email processing (batch size: {batch_size})")
        
        # Step 1: Find Due Emails
        due_emails = get_due_scheduled_emails(db, limit=batch_size)
        
        if not due_emails:
            logger.info("No due scheduled emails found")
            return ScheduledEmailProcessingResult(
                processed_count=0,
                queued_count=0,
                failed_count=0,
                errors=[],
                processing_time_seconds=0.0
            )
        
        logger.info(f"Found {len(due_emails)} due scheduled emails")
        
        # Step 2: Move to Queue
        for scheduled_email in due_emails:
            try:
                processed_count += 1
                
                # Convert ScheduledEmailType to EmailType
                email_type = _convert_scheduled_to_queue_type(scheduled_email.email_type)
                
                # Create email queue entry
                queue_entry = EmailQueue(
                    user_email=scheduled_email.email_address,
                    user_name=scheduled_email.recipient_name,
                    email_type=email_type,
                    subject=scheduled_email.subject,
                    body=scheduled_email.content,
                    scheduled_time=datetime.now(UTC),  # Queue immediately
                    status=EmailStatus.pending,
                    max_retries=scheduled_email.max_retries
                )
                
                db.add(queue_entry)
                db.flush()  # Get the ID without committing
                
                # Mark scheduled email as queued
                if mark_scheduled_email_as_queued(db, scheduled_email.id, queue_entry.id):
                    queued_count += 1
                    logger.info(
                        f"Moved scheduled email {scheduled_email.id} to queue {queue_entry.id} "
                        f"({scheduled_email.email_type.value} for {scheduled_email.email_address})"
                    )
                else:
                    failed_count += 1
                    error_msg = f"Failed to mark scheduled email {scheduled_email.id} as queued"
                    errors.append(error_msg)
                    logger.error(error_msg)
                
            except Exception as e:
                failed_count += 1
                error_msg = f"Error processing scheduled email {scheduled_email.id}: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
                
                # Rollback this specific email's changes
                db.rollback()
                continue
        
        # Commit all successful changes
        if queued_count > 0:
            db.commit()
            logger.info(f"Successfully queued {queued_count} scheduled emails")
        
        # Handle recurring emails
        _handle_recurring_emails(db, due_emails)
        
    except Exception as e:
        db.rollback()
        error_msg = f"Critical error in scheduled email processing: {str(e)}"
        errors.append(error_msg)
        logger.error(error_msg)
        failed_count = processed_count
        queued_count = 0
    
    # Calculate processing time
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    result = ScheduledEmailProcessingResult(
        processed_count=processed_count,
        queued_count=queued_count,
        failed_count=failed_count,
        errors=errors,
        processing_time_seconds=processing_time
    )
    
    logger.info(
        f"Scheduled email processing completed: "
        f"{queued_count} queued, {failed_count} failed, "
        f"{processing_time:.2f}s"
    )
    
    return result


def _convert_scheduled_to_queue_type(scheduled_type) -> EmailType:
    """
    Convert ScheduledEmailType to EmailType.
    
    Args:
        scheduled_type: ScheduledEmailType enum value
        
    Returns:
        EmailType: Corresponding EmailType enum value
    """
    # Map scheduled email types to queue email types
    type_mapping = {
        'welcome': EmailType.welcome,
        'search_engine': EmailType.search_engine,
        'portfolio_builder': EmailType.portfolio_builder,
        'platform_complete': EmailType.platform_complete,
        # For new types not in EmailType, default to welcome
        'reminder': EmailType.welcome,
        'newsletter': EmailType.welcome,
        'announcement': EmailType.welcome,
        'follow_up': EmailType.welcome,
    }
    
    return type_mapping.get(scheduled_type.value, EmailType.welcome)


def _handle_recurring_emails(db: Session, processed_emails: List[ScheduledEmail]) -> None:
    """
    Handle recurring emails by creating new scheduled entries.
    
    Args:
        db: Database session
        processed_emails: List of emails that were just processed
    """
    from datetime import timedelta
    
    for email in processed_emails:
        if not email.is_recurring or not email.recurrence_pattern:
            continue
        
        try:
            # Calculate next occurrence
            next_scheduled_at = _calculate_next_occurrence(
                email.scheduled_at, 
                email.recurrence_pattern
            )
            
            if next_scheduled_at:
                # Create new scheduled email for next occurrence
                new_scheduled_email = ScheduledEmail(
                    email_address=email.email_address,
                    recipient_name=email.recipient_name,
                    email_type=email.email_type,
                    subject=email.subject,
                    content=email.content,
                    scheduled_at=next_scheduled_at,
                    is_recurring=True,
                    recurrence_pattern=email.recurrence_pattern,
                    max_retries=email.max_retries,
                    status=ScheduledEmailStatus.scheduled
                )
                
                db.add(new_scheduled_email)
                
                logger.info(
                    f"Created recurring email for {email.email_address} "
                    f"scheduled at {next_scheduled_at}"
                )
        
        except Exception as e:
            logger.error(f"Error creating recurring email for {email.id}: {e}")
    
    try:
        db.commit()
    except Exception as e:
        logger.error(f"Error committing recurring emails: {e}")
        db.rollback()


def _calculate_next_occurrence(current_date: datetime, pattern: str) -> datetime:
    """
    Calculate the next occurrence date based on recurrence pattern.
    
    Args:
        current_date: Current scheduled date
        pattern: Recurrence pattern ('daily', 'weekly', 'monthly', 'yearly')
        
    Returns:
        datetime: Next occurrence date
    """
    from datetime import timedelta
    from dateutil.relativedelta import relativedelta
    
    if pattern == 'daily':
        return current_date + timedelta(days=1)
    elif pattern == 'weekly':
        return current_date + timedelta(weeks=1)
    elif pattern == 'monthly':
        return current_date + relativedelta(months=1)
    elif pattern == 'yearly':
        return current_date + relativedelta(years=1)
    else:
        logger.warning(f"Unknown recurrence pattern: {pattern}")
        return None


def get_processing_status(db: Session) -> dict:
    """
    Get current status of scheduled email processing.
    
    Args:
        db: Database session
        
    Returns:
        dict: Status information
    """
    current_time = datetime.now(UTC)
    
    # Count due emails
    due_count = db.query(ScheduledEmail).filter(
        ScheduledEmail.status == ScheduledEmailStatus.scheduled,
        ScheduledEmail.scheduled_at <= current_time
    ).count()
    
    # Count scheduled emails
    scheduled_count = db.query(ScheduledEmail).filter(
        ScheduledEmail.status == ScheduledEmailStatus.scheduled
    ).count()
    
    # Count queued emails
    queued_count = db.query(ScheduledEmail).filter(
        ScheduledEmail.status == ScheduledEmailStatus.queued
    ).count()
    
    # Get next due email
    next_due_email = db.query(ScheduledEmail).filter(
        ScheduledEmail.status == ScheduledEmailStatus.scheduled,
        ScheduledEmail.scheduled_at > current_time
    ).order_by(ScheduledEmail.scheduled_at.asc()).first()
    
    next_due_at = next_due_email.scheduled_at if next_due_email else None
    
    return {
        'current_time': current_time,
        'due_now': due_count,
        'total_scheduled': scheduled_count,
        'total_queued': queued_count,
        'next_due_at': next_due_at,
        'next_due_in_seconds': (
            int((next_due_at - current_time).total_seconds()) 
            if next_due_at else None
        )
    }
