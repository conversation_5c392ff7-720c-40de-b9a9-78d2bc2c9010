"""
Scheduled Email Service
======================

Service for managing scheduled emails - the emails that will be sent at specific future dates.
This service handles CRUD operations for the scheduled_emails table.
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from datetime import datetime, timedelta
from typing import List, Optional, Tuple, Dict
import logging
import pytz

from app.models.scheduled_email import ScheduledEmail, ScheduledEmailType, ScheduledEmailStatus
from app.schemas.scheduled_email import (
    ScheduledEmailCreate, ScheduledEmailUpdate, ScheduledEmailResponse,
    ScheduledEmailStats, ScheduledEmailProcessingResult, ScheduledEmailBatch
)

logger = logging.getLogger(__name__)

# Timezone for consistent datetime handling
IST = pytz.timezone('Asia/Kolkata')
UTC = pytz.UTC


def create_scheduled_email(db: Session, email_data: ScheduledEmailCreate) -> ScheduledEmail:
    """
    Create a new scheduled email entry.
    
    Args:
        db: Database session
        email_data: Scheduled email creation data
        
    Returns:
        ScheduledEmail: Created scheduled email record
    """
    try:
        # Ensure scheduled_at is timezone-aware
        scheduled_at = email_data.scheduled_at
        if scheduled_at.tzinfo is None:
            scheduled_at = UTC.localize(scheduled_at)
        
        scheduled_email = ScheduledEmail(
            email_address=email_data.email_address,
            recipient_name=email_data.recipient_name,
            email_type=email_data.email_type,
            subject=email_data.subject,
            content=email_data.content,
            scheduled_at=scheduled_at,
            is_recurring=email_data.is_recurring,
            recurrence_pattern=email_data.recurrence_pattern,
            max_retries=email_data.max_retries,
            status=ScheduledEmailStatus.scheduled
        )
        
        db.add(scheduled_email)
        db.commit()
        db.refresh(scheduled_email)
        
        logger.info(
            f"Scheduled email created: {email_data.email_type.value} for {email_data.email_address} "
            f"scheduled at {scheduled_at}"
        )
        
        return scheduled_email
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating scheduled email: {e}")
        raise


def get_scheduled_email(db: Session, email_id: int) -> Optional[ScheduledEmail]:
    """Get a scheduled email by ID."""
    return db.query(ScheduledEmail).filter(ScheduledEmail.id == email_id).first()


def get_scheduled_emails(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    status: Optional[ScheduledEmailStatus] = None,
    email_type: Optional[ScheduledEmailType] = None,
    email_address: Optional[str] = None,
    due_before: Optional[datetime] = None
) -> List[ScheduledEmail]:
    """
    Get scheduled emails with optional filtering.
    
    Args:
        db: Database session
        skip: Number of records to skip
        limit: Maximum number of records to return
        status: Filter by status
        email_type: Filter by email type
        email_address: Filter by email address
        due_before: Filter emails due before this datetime
        
    Returns:
        List[ScheduledEmail]: List of scheduled emails
    """
    query = db.query(ScheduledEmail)
    
    # Apply filters
    if status:
        query = query.filter(ScheduledEmail.status == status)
    
    if email_type:
        query = query.filter(ScheduledEmail.email_type == email_type)
    
    if email_address:
        query = query.filter(ScheduledEmail.email_address.ilike(f"%{email_address}%"))
    
    if due_before:
        # Ensure due_before is timezone-aware
        if due_before.tzinfo is None:
            due_before = UTC.localize(due_before)
        query = query.filter(ScheduledEmail.scheduled_at <= due_before)
    
    # Order by scheduled_at ascending (earliest first)
    query = query.order_by(asc(ScheduledEmail.scheduled_at))
    
    return query.offset(skip).limit(limit).all()


def get_due_scheduled_emails(db: Session, limit: int = 100) -> List[ScheduledEmail]:
    """
    Get scheduled emails that are due for processing (scheduled_at <= now).
    
    Args:
        db: Database session
        limit: Maximum number of emails to return
        
    Returns:
        List[ScheduledEmail]: List of due scheduled emails
    """
    current_time = datetime.now(UTC)
    
    return db.query(ScheduledEmail).filter(
        and_(
            ScheduledEmail.status == ScheduledEmailStatus.scheduled,
            ScheduledEmail.scheduled_at <= current_time
        )
    ).order_by(asc(ScheduledEmail.scheduled_at)).limit(limit).all()


def update_scheduled_email(
    db: Session, 
    email_id: int, 
    email_data: ScheduledEmailUpdate
) -> Optional[ScheduledEmail]:
    """
    Update a scheduled email.
    
    Args:
        db: Database session
        email_id: ID of the scheduled email to update
        email_data: Update data
        
    Returns:
        Optional[ScheduledEmail]: Updated scheduled email or None if not found
    """
    try:
        scheduled_email = db.query(ScheduledEmail).filter(ScheduledEmail.id == email_id).first()
        
        if not scheduled_email:
            return None
        
        # Update fields if provided
        update_data = email_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            if field == 'scheduled_at' and value:
                # Ensure timezone-aware
                if value.tzinfo is None:
                    value = UTC.localize(value)
            
            setattr(scheduled_email, field, value)
        
        db.commit()
        db.refresh(scheduled_email)
        
        logger.info(f"Scheduled email {email_id} updated")
        
        return scheduled_email
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating scheduled email {email_id}: {e}")
        raise


def cancel_scheduled_email(db: Session, email_id: int) -> bool:
    """
    Cancel a scheduled email.
    
    Args:
        db: Database session
        email_id: ID of the scheduled email to cancel
        
    Returns:
        bool: True if cancelled successfully, False if not found
    """
    try:
        scheduled_email = db.query(ScheduledEmail).filter(ScheduledEmail.id == email_id).first()
        
        if not scheduled_email:
            return False
        
        if scheduled_email.status != ScheduledEmailStatus.scheduled:
            logger.warning(f"Cannot cancel scheduled email {email_id}: status is {scheduled_email.status}")
            return False
        
        scheduled_email.status = ScheduledEmailStatus.cancelled
        scheduled_email.processed_at = datetime.now(UTC)
        
        db.commit()
        
        logger.info(f"Scheduled email {email_id} cancelled")
        
        return True
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error cancelling scheduled email {email_id}: {e}")
        raise


def mark_scheduled_email_as_queued(
    db: Session, 
    email_id: int, 
    queued_email_id: int
) -> bool:
    """
    Mark a scheduled email as queued (moved to pending_emails).
    
    Args:
        db: Database session
        email_id: ID of the scheduled email
        queued_email_id: ID of the corresponding email_queue record
        
    Returns:
        bool: True if marked successfully, False if not found
    """
    try:
        scheduled_email = db.query(ScheduledEmail).filter(ScheduledEmail.id == email_id).first()
        
        if not scheduled_email:
            return False
        
        scheduled_email.status = ScheduledEmailStatus.queued
        scheduled_email.processed_at = datetime.now(UTC)
        scheduled_email.queued_email_id = queued_email_id
        
        db.commit()
        
        logger.info(f"Scheduled email {email_id} marked as queued (queue ID: {queued_email_id})")
        
        return True
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error marking scheduled email {email_id} as queued: {e}")
        raise


def get_scheduled_email_stats(db: Session) -> ScheduledEmailStats:
    """
    Get statistics about scheduled emails.
    
    Args:
        db: Database session
        
    Returns:
        ScheduledEmailStats: Statistics about scheduled emails
    """
    current_time = datetime.now(UTC)
    hour_from_now = current_time + timedelta(hours=1)
    day_from_now = current_time + timedelta(days=1)
    
    # Count by status
    total_scheduled = db.query(ScheduledEmail).filter(
        ScheduledEmail.status == ScheduledEmailStatus.scheduled
    ).count()
    
    total_queued = db.query(ScheduledEmail).filter(
        ScheduledEmail.status == ScheduledEmailStatus.queued
    ).count()
    
    total_cancelled = db.query(ScheduledEmail).filter(
        ScheduledEmail.status == ScheduledEmailStatus.cancelled
    ).count()
    
    total_expired = db.query(ScheduledEmail).filter(
        ScheduledEmail.status == ScheduledEmailStatus.expired
    ).count()
    
    # Count due emails
    due_now = db.query(ScheduledEmail).filter(
        and_(
            ScheduledEmail.status == ScheduledEmailStatus.scheduled,
            ScheduledEmail.scheduled_at <= current_time
        )
    ).count()
    
    due_within_hour = db.query(ScheduledEmail).filter(
        and_(
            ScheduledEmail.status == ScheduledEmailStatus.scheduled,
            ScheduledEmail.scheduled_at <= hour_from_now,
            ScheduledEmail.scheduled_at > current_time
        )
    ).count()
    
    due_within_day = db.query(ScheduledEmail).filter(
        and_(
            ScheduledEmail.status == ScheduledEmailStatus.scheduled,
            ScheduledEmail.scheduled_at <= day_from_now,
            ScheduledEmail.scheduled_at > hour_from_now
        )
    ).count()
    
    # Count by type
    by_type = {}
    for email_type in ScheduledEmailType:
        count = db.query(ScheduledEmail).filter(
            and_(
                ScheduledEmail.email_type == email_type,
                ScheduledEmail.status == ScheduledEmailStatus.scheduled
            )
        ).count()
        by_type[email_type] = count
    
    return ScheduledEmailStats(
        total_scheduled=total_scheduled,
        total_queued=total_queued,
        total_cancelled=total_cancelled,
        total_expired=total_expired,
        due_now=due_now,
        due_within_hour=due_within_hour,
        due_within_day=due_within_day,
        by_type=by_type
    )


def delete_scheduled_email(db: Session, email_id: int) -> bool:
    """
    Delete a scheduled email permanently.
    
    Args:
        db: Database session
        email_id: ID of the scheduled email to delete
        
    Returns:
        bool: True if deleted successfully, False if not found
    """
    try:
        scheduled_email = db.query(ScheduledEmail).filter(ScheduledEmail.id == email_id).first()
        
        if not scheduled_email:
            return False
        
        db.delete(scheduled_email)
        db.commit()
        
        logger.info(f"Scheduled email {email_id} deleted permanently")
        
        return True
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting scheduled email {email_id}: {e}")
        raise
