"""
Scheduled Email Schemas
======================

Pydantic schemas for scheduled email operations.
"""

from pydantic import BaseModel, EmailStr, Field, validator
from datetime import datetime
from typing import Optional, List
from app.models.scheduled_email import ScheduledEmailType, ScheduledEmailStatus


class ScheduledEmailBase(BaseModel):
    """Base schema for scheduled email."""
    email_address: EmailStr
    recipient_name: str = Field(..., min_length=1, max_length=255)
    email_type: ScheduledEmailType
    subject: str = Field(..., min_length=1, max_length=500)
    content: str = Field(..., min_length=1)
    scheduled_at: datetime
    is_recurring: bool = False
    recurrence_pattern: Optional[str] = Field(None, max_length=100)
    max_retries: int = Field(3, ge=0, le=10)


class ScheduledEmailCreate(ScheduledEmailBase):
    """Schema for creating a scheduled email."""
    
    @validator('scheduled_at')
    def validate_future_date(cls, v):
        """Ensure scheduled_at is in the future."""
        from datetime import datetime
        import pytz
        
        current_time = datetime.now(pytz.UTC)
        
        # Handle timezone-aware comparison
        if v.tzinfo is None:
            # If naive, assume UTC
            v = pytz.UTC.localize(v)
        
        if v <= current_time:
            raise ValueError('scheduled_at must be in the future')
        
        return v
    
    @validator('recurrence_pattern')
    def validate_recurrence_pattern(cls, v, values):
        """Validate recurrence pattern if recurring is enabled."""
        if values.get('is_recurring', False) and not v:
            raise ValueError('recurrence_pattern is required when is_recurring is True')
        
        if v and v not in ['daily', 'weekly', 'monthly', 'yearly']:
            raise ValueError('recurrence_pattern must be one of: daily, weekly, monthly, yearly')
        
        return v


class ScheduledEmailUpdate(BaseModel):
    """Schema for updating a scheduled email."""
    email_address: Optional[EmailStr] = None
    recipient_name: Optional[str] = Field(None, min_length=1, max_length=255)
    email_type: Optional[ScheduledEmailType] = None
    subject: Optional[str] = Field(None, min_length=1, max_length=500)
    content: Optional[str] = Field(None, min_length=1)
    scheduled_at: Optional[datetime] = None
    status: Optional[ScheduledEmailStatus] = None
    is_recurring: Optional[bool] = None
    recurrence_pattern: Optional[str] = Field(None, max_length=100)
    max_retries: Optional[int] = Field(None, ge=0, le=10)
    error_message: Optional[str] = None
    
    @validator('scheduled_at')
    def validate_future_date(cls, v):
        """Ensure scheduled_at is in the future if provided."""
        if v is None:
            return v
            
        from datetime import datetime
        import pytz
        
        current_time = datetime.now(pytz.UTC)
        
        # Handle timezone-aware comparison
        if v.tzinfo is None:
            # If naive, assume UTC
            v = pytz.UTC.localize(v)
        
        if v <= current_time:
            raise ValueError('scheduled_at must be in the future')
        
        return v


class ScheduledEmailResponse(BaseModel):
    """Schema for scheduled email response."""
    id: int
    email_address: EmailStr
    recipient_name: str
    email_type: ScheduledEmailType
    subject: str
    content: str
    scheduled_at: datetime
    status: ScheduledEmailStatus
    processed_at: Optional[datetime]
    queued_email_id: Optional[int]
    retry_count: int
    max_retries: int
    error_message: Optional[str]
    is_recurring: bool
    recurrence_pattern: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Computed properties
    is_scheduled: bool
    is_queued: bool
    is_cancelled: bool
    is_expired: bool
    can_be_processed: bool
    is_due: bool
    time_until_due: int
    
    class Config:
        from_attributes = True


class ScheduledEmailStats(BaseModel):
    """Schema for scheduled email statistics."""
    total_scheduled: int
    total_queued: int
    total_cancelled: int
    total_expired: int
    due_now: int
    due_within_hour: int
    due_within_day: int
    by_type: dict[ScheduledEmailType, int]


class ScheduledEmailBatch(BaseModel):
    """Schema for batch operations on scheduled emails."""
    email_ids: List[int] = Field(..., min_items=1, max_items=100)
    action: str = Field(..., regex="^(cancel|reschedule|queue)$")
    new_scheduled_at: Optional[datetime] = None
    
    @validator('new_scheduled_at')
    def validate_reschedule_date(cls, v, values):
        """Validate new scheduled date for reschedule action."""
        if values.get('action') == 'reschedule' and not v:
            raise ValueError('new_scheduled_at is required for reschedule action')
        
        if v:
            from datetime import datetime
            import pytz
            
            current_time = datetime.now(pytz.UTC)
            
            # Handle timezone-aware comparison
            if v.tzinfo is None:
                # If naive, assume UTC
                v = pytz.UTC.localize(v)
            
            if v <= current_time:
                raise ValueError('new_scheduled_at must be in the future')
        
        return v


class ScheduledEmailProcessingResult(BaseModel):
    """Schema for scheduled email processing results."""
    processed_count: int
    queued_count: int
    failed_count: int
    errors: List[str]
    processing_time_seconds: float


class ScheduledEmailListResponse(BaseModel):
    """Schema for paginated scheduled email list."""
    emails: List[ScheduledEmailResponse]
    total: int
    page: int
    per_page: int
    total_pages: int
    has_next: bool
    has_prev: bool
