# Scheduled Email System Implementation

## Overview

This document describes the complete implementation of the **Scheduled Email System** as specified in the system design. The system handles emails scheduled for specific future dates and integrates seamlessly with the existing immediate email processing system.

## System Architecture

The system consists of three main components:

### 1. Ingestion Algorithm ✅ (Existing + Enhanced)
- **Immediate emails** → `email_queue` table (existing)
- **Scheduled emails** → `scheduled_emails` table (new)

### 2. Immediate Mail Algorithm ✅ (Unchanged)
- Processes emails from `email_queue` table in batches of 10 every 10 seconds
- Handled by existing `email_processor.py`

### 3. Scheduled Mail Algorithm ✅ (New Implementation)
- **Scheduler**: Runs every minute (configurable)
- **Scheduled Monitor (Watchman 2)**: Moves due emails from `scheduled_emails` to `email_queue`

## Database Schema

### New Table: `scheduled_emails`

```sql
CREATE TABLE scheduled_emails (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email_address VARCHAR(255) NOT NULL,
    recipient_name VARCHAR(255) NOT NULL,
    email_type ENUM('welcome', 'search_engine', 'portfolio_builder', 'platform_complete', 'reminder', 'newsletter', 'announcement', 'follow_up') NOT NULL,
    subject VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    status ENUM('scheduled', 'queued', 'cancelled', 'expired') DEFAULT 'scheduled',
    processed_at TIMESTAMP NULL,
    queued_email_id INT NULL,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    error_message TEXT NULL,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Existing Table: `email_queue` (unchanged)
- Serves as the "pending_emails" table from the design
- Processed by existing email processor

## Implementation Files

### Core Models
- `app/models/scheduled_email.py` - ScheduledEmail model with status tracking
- `app/schemas/scheduled_email.py` - Pydantic schemas for API operations

### Services
- `app/services/scheduled_email_service.py` - CRUD operations for scheduled emails
- `app/services/scheduled_monitor_service.py` - **Watchman 2** implementation

### API Endpoints
- `app/api/scheduled_emails.py` - REST API for managing scheduled emails

### Scheduler
- `scheduled_email_scheduler.py` - Main scheduler that runs the monitor
- `run_scheduled_email_scheduler.bat` - Startup script

### Database Migration
- `BETA-SQL/add_scheduled_emails_table.sql` - Creates the new table with sample data

### Testing
- `test_scheduled_email_system.py` - Comprehensive test suite

## API Endpoints

### Scheduled Email Management
```
POST   /scheduled-emails/              # Create scheduled email
GET    /scheduled-emails/              # List scheduled emails (with filtering)
GET    /scheduled-emails/{id}          # Get specific scheduled email
PUT    /scheduled-emails/{id}          # Update scheduled email
DELETE /scheduled-emails/{id}          # Delete scheduled email
POST   /scheduled-emails/{id}/cancel   # Cancel scheduled email
```

### Monitoring & Statistics
```
GET    /scheduled-emails/stats/overview     # Get statistics
GET    /scheduled-emails/due/list           # List due emails
POST   /scheduled-emails/process/due        # Manually trigger processing
GET    /scheduled-emails/status/processing  # Get processing status
POST   /scheduled-emails/batch              # Batch operations
```

## Example Usage

### Creating a Scheduled Email
```python
from datetime import datetime, timedelta
import pytz

# Schedule an email for August 3, 2025 at 9:00 AM
scheduled_email = {
    "email_address": "<EMAIL>",
    "recipient_name": "User One", 
    "email_type": "reminder",
    "subject": "Event reminder...",
    "content": "This is a reminder about the upcoming event.",
    "scheduled_at": datetime(2025, 8, 3, 9, 0, 0, tzinfo=pytz.UTC)
}

# POST to /scheduled-emails/
```

### How the System Works

1. **Email Creation**: Scheduled emails are stored in `scheduled_emails` table
2. **Scheduler Runs**: Every minute, the scheduler checks for due emails
3. **Monitor Processing**: Due emails (where `scheduled_at <= NOW()`) are found
4. **Queue Transfer**: Due emails are moved to `email_queue` with status 'pending'
5. **Status Update**: Original emails marked as 'queued' in `scheduled_emails`
6. **Normal Processing**: Existing email processor handles queued emails

## Installation & Setup

### 1. Database Migration
```bash
# Run the SQL migration
mysql -u username -p database_name < BETA-SQL/add_scheduled_emails_table.sql
```

### 2. Update FastAPI Application
The main application (`app/main.py`) has been updated to include the new API routes.

### 3. Start the Scheduler
```bash
# Windows
run_scheduled_email_scheduler.bat

# Linux/Mac
python scheduled_email_scheduler.py --interval 60 --batch-size 50
```

### 4. Test the System
```bash
python test_scheduled_email_system.py
```

## Configuration Options

### Scheduler Configuration
- `--interval`: Check interval in seconds (default: 60)
- `--batch-size`: Max emails to process per cycle (default: 50)
- `--dry-run`: Test mode without actually moving emails
- `--max-cycles`: Limit cycles for testing

### Environment Variables
- `DATABASE_URL`: Database connection string
- `ENVIRONMENT`: Set to 'production' for production mode

## Monitoring & Logging

### Log Files
- `scheduled_email_scheduler.log` - Scheduler activity logs
- Application logs include scheduled email processing

### Monitoring Endpoints
- `/scheduled-emails/stats/overview` - System statistics
- `/scheduled-emails/status/processing` - Current processing status

## Error Handling

### Retry Logic
- Failed email processing retries up to `max_retries` times
- Exponential backoff for retry attempts
- Detailed error logging

### Status Tracking
- `scheduled`: Email waiting for scheduled time
- `queued`: Email moved to pending queue
- `cancelled`: Email cancelled by user
- `expired`: Email failed processing and expired

## Performance Considerations

### Database Indexes
- Optimized indexes for due email queries
- Efficient status and type filtering
- Proper timezone handling

### Batch Processing
- Configurable batch sizes to prevent overload
- Graceful shutdown handling
- Memory-efficient processing

## Integration with Existing System

### Backward Compatibility
- Existing `email_queue` system unchanged
- Existing email processor unchanged
- No impact on immediate email processing

### Seamless Operation
- Scheduled emails automatically join the normal processing flow
- Same email templates and sending logic
- Unified monitoring and logging

## Testing

The system includes comprehensive tests covering:
- Scheduled email creation and management
- Due email detection and processing
- Integration with existing email queue
- Error handling and edge cases
- Performance and load testing

Run tests with:
```bash
python test_scheduled_email_system.py
```

## Future Enhancements

### Recurring Emails
- Support for daily, weekly, monthly, yearly patterns
- Automatic creation of next occurrence
- Flexible recurrence rules

### Advanced Scheduling
- Time zone support for recipients
- Business day scheduling
- Holiday awareness

### Analytics
- Delivery rate tracking
- Performance metrics
- Usage statistics

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure DATABASE_URL is correct
2. **Timezone Issues**: All times stored in UTC
3. **Permission Issues**: Check database user permissions
4. **Port Conflicts**: Ensure scheduler doesn't conflict with main app

### Debug Commands
```bash
# Check due emails
python -c "from app.services.scheduled_monitor_service import get_processing_status; print(get_processing_status(db))"

# Manual processing
curl -X POST "http://localhost:8000/scheduled-emails/process/due"

# View statistics
curl "http://localhost:8000/scheduled-emails/stats/overview"
```

## Conclusion

The Scheduled Email System provides a robust, scalable solution for handling future-dated emails while maintaining full compatibility with the existing immediate email processing system. The implementation follows the exact specifications from the system design and provides comprehensive monitoring, error handling, and testing capabilities.
