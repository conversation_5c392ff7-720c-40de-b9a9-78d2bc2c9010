version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: lawvriksh_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - lawvriksh_network
    command: --default-authentication-plugin=mysql_native_password --max-connections=300
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis (for caching and session management)
  redis:
    image: redis:7-alpine
    container_name: lawvriksh_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lawvriksh_network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 20s
      retries: 10

  # LawVriksh Backend with Integrated Email Processing
  backend:
    build:
      context: ..
      dockerfile: deployment/Dockerfile
    container_name: lawvriksh_backend
    restart: unless-stopped
    environment:
      # Database Configuration
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${MYSQL_DATABASE}
      DB_USER: ${MYSQL_USER}
      DB_PASSWORD: ${MYSQL_PASSWORD}
      
      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
      # Application Configuration
      SECRET_KEY: ${SECRET_KEY}
      FRONTEND_URL: ${FRONTEND_URL}
      ENVIRONMENT: production
      
      # Email Configuration
      SMTP_SERVER: ${SMTP_SERVER}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USERNAME: ${SMTP_USERNAME}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      SMTP_USE_TLS: ${SMTP_USE_TLS}
      FROM_EMAIL: ${FROM_EMAIL}
      FROM_NAME: ${FROM_NAME}
      
      # Security
      ALLOWED_HOSTS: ${ALLOWED_HOSTS}
      CORS_ORIGINS: ${CORS_ORIGINS}
      
      # Logging
      LOG_LEVEL: INFO
      
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./cache:/app/cache
    ports:
      - "8000:8000"
    networks:
      - lawvriksh_network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      timeout: 30s
      retries: 5
      start_period: 30s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: lawvriksh_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/sites-available:/etc/nginx/sites-available
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    networks:
      - lawvriksh_network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      timeout: 30s
      retries: 3

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  lawvriksh_network:
    driver: bridge
