import requests
import json
import concurrent.futures
import time
import random
import string

# --- Configuration ---
# Base URL of the LOCAL target backend
BASE_URL = "http://localhost:8000"
# API endpoint for user signup (including the /api prefix)
SIGNUP_ENDPOINT = "/api/auth/signup"
# Number of concurrent signup requests to simulate
NUMBER_OF_USERS = 200
# Specific email addresses to be used for designated request numbers (1-indexed)
# Note: Using the same email for requests 49 and 50 will likely cause the latter to fail
# if the backend enforces unique email addresses.
SPECIFIC_EMAILS = {
    30: "<EMAIL>",
    40: "<EMAIL>",
    49: "<EMAIL>", # Will be used for the 49th request (index 48)
    50: "<EMAIL>"  # Will be used for the 50th request (index 49)
}
# Default password used for all generated test accounts
PASSWORD = "securepassword123"
# ---------------------

def generate_random_email():
    """
    Generates a unique, random email address for testing purposes.
    Returns:
        str: A randomly generated email address.
    """
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    domain = "test200local.example.com" # Test domain for local testing
    return f"{username}@{domain}"

def generate_random_name():
    """
    Generates a plausible, random full name.
    Returns:
        str: A randomly generated name (e.g., "John Smith").
    """
    # Expanded list of names for variety with 200 users
    first_names = [
        "John", "Jane", "Alice", "Bob", "Charlie", "Diana", "Eve", "Frank",
        "Grace", "Henry", "Ivy", "Jack", "Kate", "Leo", "Mia", "Noah",
        "Olivia", "Paul", "Quinn", "Ryan", "Sophia", "Thomas", "Uma", "Victor",
        "Wendy", "Xavier", "Yara", "Zane", "Aria", "Bennett", "Chloe", "Daniel",
        "Elara", "Finn", "Georgia", "Hugo", "Isla", "Jasper", "Kira", "Liam"
    ]
    last_names = [
        "Smith", "Johnson", "Williams", "Brown", "Jones", "Miller", "Davis",
        "Garcia", "Rodriguez", "Wilson", "Martinez", "Anderson", "Taylor",
        "Thomas", "Hernandez", "Moore", "Martin", "Jackson", "Thompson", "White",
        "Lopez", "Lee", "Gonzalez", "Harris", "Clark", "Lewis", "Robinson",
        "Walker", "Perez", "Hall", "Young", "Allen", "Sanchez", "Wright",
        "King", "Scott", "Green", "Baker", "Adams", "Nelson"
    ]
    return f"{random.choice(first_names)} {random.choice(last_names)}"

def create_user_data(index):
    """
    Creates a dictionary containing user signup data.
    Random data is generated by default, but specific emails are inserted
    for predefined request indices.

    Args:
        index (int): The 0-based index of the request/user.

    Returns:
        dict: A dictionary with 'name', 'email', and 'password' keys.
    """
    # Generate base random user data
    data = {
        "name": generate_random_name(),
        "email": generate_random_email(), # Temporary email
        "password": PASSWORD
    }

    # Override email if this request index matches a specific one
    # Convert 0-based index to 1-based request number for lookup
    request_number = index + 1
    if request_number in SPECIFIC_EMAILS:
        data["email"] = SPECIFIC_EMAILS[request_number]

    return data

def signup_user(user_data):
    """
    Performs the actual HTTP POST request to the signup endpoint and measures time.

    Args:
        user_data (dict): The user data to send in the request body.

    Returns:
        tuple: A tuple containing:
            - status_code (int): The HTTP status code received (-1 for request errors).
            - response_json (dict/str): The parsed JSON response, error text, or error dict.
            - user_data_sent (dict): The user data that was sent.
            - request_duration (float): Time taken for the request in seconds.
    """
    url = f"{BASE_URL}{SIGNUP_ENDPOINT}"
    headers = {'Content-Type': 'application/json'}

    start_request_time = time.time() # Start timing the individual request

    try:
        # Send the POST request with a timeout
        # Consider increasing timeout if your local server is slow
        response = requests.post(url, headers=headers, data=json.dumps(user_data), timeout=30)

        end_request_time = time.time() # End timing after receiving response
        request_duration = end_request_time - start_request_time # Calculate duration

        # Attempt to parse the response as JSON
        try:
            response_data = response.json()
        except json.JSONDecodeError:
            # If not JSON, capture the raw text
            response_data = {"error": "Response not JSON", "text": response.text}

        # Return results including the data sent and the duration
        return response.status_code, response_data, user_data, request_duration

    except requests.exceptions.RequestException as e:
        # Catch network-related errors (timeouts, DNS failures, connection errors, etc.)
        end_request_time = time.time() # End timing even on error
        request_duration = end_request_time - start_request_time
        error_info = {"error": f"Request failed: {str(e)}"}
        return -1, error_info, user_data, request_duration # Include duration for errors too

def main():
    """
    Main function to orchestrate the load test against the LOCAL backend.
    It prepares data, sends concurrent requests, and reports results.
    """
    print(f"--- Local LawVriksh Signup Load Test (N={NUMBER_OF_USERS}) ---")
    print(f"Target Endpoint: {BASE_URL}{SIGNUP_ENDPOINT}")
    print(f"Concurrent Requests: {NUMBER_OF_USERS}")
    print(f"Special Emails Assigned:")
    for req_num, email in SPECIFIC_EMAILS.items():
        print(f"  Request #{req_num}: {email}")
    print("-" * 60) # Adjusted separator length

    # --- Preparation ---
    # Generate all user data dictionaries beforehand
    print("Preparing user data...")
    all_user_data = [create_user_data(i) for i in range(NUMBER_OF_USERS)]
    print("User data prepared.\n")

    # --- Execution ---
    print("Sending concurrent signup requests to LOCAL backend...")
    overall_start_time = time.time() # Start timing the overall process
    results = [] # Will store (status_code, response_json, user_data, duration)

    # Use ThreadPoolExecutor for concurrent execution
    with concurrent.futures.ThreadPoolExecutor(max_workers=NUMBER_OF_USERS) as executor:
        # Submit all tasks to the executor
        future_to_data = {executor.submit(signup_user, data): data for data in all_user_data}

        # Collect results as they complete
        for future in concurrent.futures.as_completed(future_to_data):
            # Unpack the result tuple which now includes duration
            status_code, response_json, user_data_sent, duration = future.result()
            # Store the result for final reporting
            results.append((status_code, response_json, user_data_sent, duration))

    overall_end_time = time.time() # End timing the overall process
    total_overall_time = overall_end_time - overall_start_time
    print("All requests completed.\n")

    # --- Reporting ---
    print("--- Test Execution Summary ---")
    print(f"Total Requests Sent: {len(results)}")
    print(f"Total Wall Clock Time (Start to Finish): {total_overall_time:.4f} seconds")

    success_count = 0
    failure_count = 0 # HTTP error status codes (4xx, 5xx)
    error_count = 0   # Request-level errors (timeouts, network issues)
    total_duration_all_requests = 0.0 # Sum of durations for ALL requests
    successful_durations = [] # Store durations of successful requests

    # Initialize variables for tracking min/max times and requests under 1s
    if results:
        min_time = float('inf')
        max_time = float('-inf')
    else:
        min_time = max_time = None
    requests_under_1s = 0

    # --- Detailed Specific Email Results ---
    print("\n--- Results for Specific Emails ---")
    specific_results = []
    for i, (status_code, response_json, user_data_sent, duration) in enumerate(results):
        request_number = i + 1
        if request_number in [30, 40, 49, 50]:
            specific_results.append((request_number, status_code, user_data_sent['email'], duration))

    # Sort and print results for specific requests
    for req_num, status, email, duration in sorted(specific_results):
        status_desc = "SUCCESS" if status in [200, 201] else ("ERROR" if status == -1 else f"FAILED ({status})")
        print(f"Req #{req_num} (Email: {email:<30}) - Status: {status_desc:<12} - Time: {duration:.4f}s")

    # --- Overall Statistics, Mean Time, Failure Rate, and Additional Metrics ---
    print("\n--- Performance Metrics ---")
    # Process results to calculate metrics
    for status_code, response_json, user_data_sent, duration in results:
        total_duration_all_requests += duration # Add every request's duration

        # Track minimum and maximum time across all requests
        if results and duration is not None and duration != float('inf') and duration != float('-inf'):
            if duration < min_time:
                min_time = duration
            if duration > max_time:
                max_time = duration

        # Count requests completed under 1 second
        if duration < 1.0:
            requests_under_1s += 1

        if status_code in [200, 201]: # Common success codes
            success_count += 1
            successful_durations.append(duration) # Add to successful list
        elif status_code == -1: # Our custom code for request errors
            error_count += 1
        else: # Any other HTTP status code (4xx, 5xx)
            failure_count += 1

    # --- Calculate and Display Final Metrics ---
    total_requests = len(results)
    failed_requests = failure_count + error_count # Total failed requests

    # Mean time for ALL requests
    if total_requests > 0:
        mean_time_all_requests = total_duration_all_requests / total_requests
        print(f"Mean Time for All Requests: {mean_time_all_requests:.4f} seconds")
    else:
        print("Mean Time for All Requests: N/A (No requests sent)")

    # Best (Minimum) and Worst (Maximum) Time
    if min_time is not None and max_time is not None and total_requests > 0 and min_time != float('inf') and max_time != float('-inf'):
        print(f"Best Time (Min) for a Request: {min_time:.4f} seconds")
        print(f"Worst Time (Max) for a Request: {max_time:.4f} seconds")
    else:
        print("Best/Worst Time: N/A (No requests completed successfully)")

    # Requests Completed Under 1 Second
    print(f"Requests Completed Under 1 Second: {requests_under_1s}")

    # --- Request Status Summary ---
    print(f"\n--- Request Status Counts ---")
    print(f"Successful Requests (200/201): {success_count}")
    print(f"Failed Requests (HTTP Errors): {failure_count}")
    print(f"Request Errors (Timeouts/etc): {error_count}")
    print(f"Total Failed Requests: {failed_requests}")

    # Failure Rate Calculation
    if total_requests > 0:
        failure_rate = (failed_requests / total_requests) * 100
        print(f"Failure Rate: {failure_rate:.2f}%")
    else:
        print("Failure Rate: N/A (No requests sent)")

    # --- Successful Request Metrics (Optional) ---
    print(f"\n--- Successful Request Metrics ---")
    if success_count > 0:
        mean_time_successful = sum(successful_durations) / success_count
        print(f"Mean Time for Successful Requests: {mean_time_successful:.4f} seconds")
    else:
        print("Mean Time for Successful Requests: N/A (No successful requests)")

    print("-" * 60) # Adjusted separator length
    print("Local load test finished.")

if __name__ == "__main__":
    # Reminder: Ensure your local backend is running on http://localhost:8000
    # Uncomment the lines below for a confirmation prompt:
    # confirmation = input(f"\nConfirm: Send {NUMBER_OF_USERS} requests to LOCAL {BASE_URL}{SIGNUP_ENDPOINT}? (yes/no): ")
    # if confirmation.lower() != 'yes':
    #     print("Test cancelled by user.")
    #     exit()

    main()