                                                                                                                                                                                                                                                                                                                                                                                                                                                                       docker-compose.yml
# docker-compose.yml (for production)
version: '3.8'

services:
  # The FastAPI backend service
  backend:
    build: ./backend
    restart: always
    volumes:
      - ./backend:/app
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
      - ./backend/cache:/app/cache
    env_file:
      - ./backend/.env.production
    environment:
      # Database configuration
      - DB_HOST=db
      - DB_PORT=3306
      # Redis configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      # Background email processing
      - BACKGROUND_EMAIL_PROCESSING=true
      - EMAIL_CHECK_INTERVAL=60
    command: ["/usr/local/bin/wait-for.sh", "db", "redis", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]
    depends_on:
      db: # <-- CORRECTED
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://lawvriksh.com/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # The React frontend service
  frontend:
    build: ./frontend
    restart: always
    networks:
      - app-network

  # Redis service for caching and background processing
  redis:
    image: redis:7-alpine
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # The MySQL database service (renamed to 'db')
  db: # <-- CORRECTED
    image: mysql:8.0
    restart: always
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: lawvriksh_referral
      MYSQL_USER: lawuser
      MYSQL_PASSWORD: lawpass123
    volumes:
      - db-data:/var/lib/mysql
      - ./backend/lawdata.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  # The Nginx reverse proxy service
  nginx:
    image: nginx:latest
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    depends_on:
      - frontend
      - backend
    networks:
      - app-network

  # The Certbot service for SSL certificates
  certbot:
    image: certbot/certbot
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"

# Defines the named volumes for persistent data
volumes:
  db-data:
  redis-data:

# Defines the shared network for inter-container communication
networks:
  app-network:
    driver: bridge




