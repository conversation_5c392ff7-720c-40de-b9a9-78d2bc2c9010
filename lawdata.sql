-- =====================================================
-- LawVriksh Referral Platform Database Schema
-- MySQL 8.0+ Compatible - Production Ready
--
-- Author:      LawVriksh Development Team
-- Version:     2.0 - Ultra Performance Edition
-- Last Update: 2025-07-27
-- Description: Complete database schema with sub-second performance
--              optimizations, caching support, and production features.
-- =====================================================

-- -----------------------------------------------------
-- Initial Setup and Configuration
-- -----------------------------------------------------
CREATE DATABASE IF NOT EXISTS lawvriksh_referral
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE lawvriksh_referral;

-- MySQL Performance Settings
-- Note: These settings should be configured in my.cnf file for production
-- Removing all SET GLOBAL statements to avoid read-only variable errors
--
-- Recommended my.cnf settings:
-- [mysqld]
-- innodb_buffer_pool_size = 1G
-- innodb_log_file_size = 256M
-- innodb_flush_log_at_trx_commit = 2
-- max_connections = 1000
-- thread_cache_size = 100
-- table_open_cache = 4000

-- Drop existing tables in correct order (foreign key dependencies)
DROP TABLE IF EXISTS email_queue;
DROP TABLE IF EXISTS feedback;
DROP TABLE IF EXISTS share_events;
DROP TABLE IF EXISTS user_stats;
DROP TABLE IF EXISTS users;

-- Drop existing views
DROP VIEW IF EXISTS view_user_stats;
DROP VIEW IF EXISTS view_platform_stats;
DROP VIEW IF EXISTS v_leaderboard_fast;

-- -----------------------------------------------------
-- User and Privilege Management
-- -----------------------------------------------------
-- User management - Using existing root user from your .env configuration
-- DROP USER IF EXISTS 'lawuser'@'%';
-- CREATE USER 'lawuser'@'%' IDENTIFIED BY 'lawpass123';
-- GRANT ALL PRIVILEGES ON lawvriksh_referral.* TO 'lawuser'@'%';
-- Note: Using existing root user with password from .env file
FLUSH PRIVILEGES;

-- =====================================================
-- TABLE: users - Enhanced with Performance Optimizations
-- =====================================================
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    total_points INT NOT NULL DEFAULT 0,
    shares_count INT NOT NULL DEFAULT 0,
    default_rank INT NULL,
    current_rank INT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Performance Indexes for Sub-Second Response Times
    INDEX idx_users_email (email),
    INDEX idx_users_email_active (email, is_active),
    INDEX idx_users_id_active (id, is_active),
    INDEX idx_users_total_points (total_points DESC),
    INDEX idx_users_points_created (total_points DESC, created_at ASC),
    INDEX idx_users_is_admin (is_admin),
    INDEX idx_users_admin_active (is_admin, is_active),
    INDEX idx_users_default_rank (default_rank),
    INDEX idx_users_current_rank (current_rank),
    INDEX idx_users_created_at (created_at),
    INDEX idx_users_updated_at (updated_at),

    -- Composite index for ranking queries (most critical for performance)
    INDEX idx_users_ranking_composite (is_admin, total_points DESC, created_at ASC, id),

    -- Covering index for leaderboard queries (MySQL compatible - no INCLUDE clause)
    INDEX idx_users_leaderboard_covering (is_admin, total_points DESC, created_at ASC, id, name, shares_count, default_rank, current_rank)

    -- Note: MySQL doesn't support partial indexes with WHERE clauses or functional indexes
    -- These would need to be implemented at the application level if needed
    -- INDEX idx_users_active_email (email) WHERE is_active = TRUE,  -- Not supported in MySQL
    -- INDEX idx_users_active_points (total_points DESC, created_at ASC) WHERE is_admin = FALSE AND is_active = TRUE,  -- Not supported in MySQL
    -- INDEX idx_users_email_lower ((LOWER(email)))  -- Not supported in standard MySQL

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: user_stats - For O(1) Ranking Operations
-- =====================================================
CREATE TABLE user_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    total_users INT NOT NULL DEFAULT 0,
    total_active_users INT NOT NULL DEFAULT 0,
    total_admin_users INT NOT NULL DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Performance indexes
    INDEX idx_user_stats_last_updated (last_updated)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: share_events - Enhanced with Performance Indexes
-- =====================================================
CREATE TABLE share_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    platform ENUM('facebook', 'twitter', 'linkedin', 'instagram') NOT NULL,
    points_earned INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    -- Performance indexes for fast user share lookups
    INDEX idx_share_events_user_id (user_id),
    INDEX idx_share_events_platform (platform),
    INDEX idx_share_events_created_at (created_at),
    INDEX idx_share_events_user_id_created (user_id, created_at DESC),
    INDEX idx_share_events_platform_created (platform, created_at DESC),
    INDEX idx_share_events_user_platform (user_id, platform)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: email_queue - For Background Email Processing
-- =====================================================
CREATE TABLE email_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,

    -- Email details
    user_email VARCHAR(255) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    email_type ENUM('welcome', 'search_engine', 'portfolio_builder', 'platform_complete') NOT NULL,

    -- Email content (optional - can be generated from templates)
    subject VARCHAR(500) NULL,
    body TEXT NULL,

    -- Scheduling and status
    scheduled_time TIMESTAMP NOT NULL,
    status ENUM('pending', 'processing', 'sent', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',

    -- Retry and error handling
    retry_count INT NOT NULL DEFAULT 0,
    max_retries INT NOT NULL DEFAULT 3,
    error_message TEXT NULL,

    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Performance indexes for fast email processing
    INDEX idx_email_queue_status_scheduled (status, scheduled_time),
    INDEX idx_email_queue_user_email (user_email),
    INDEX idx_email_queue_email_type (email_type),
    INDEX idx_email_queue_scheduled_time (scheduled_time),
    INDEX idx_email_queue_status (status),
    INDEX idx_email_queue_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: feedback - For User Feedback System
-- =====================================================
CREATE TABLE feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,

    -- User identification (optional - can be anonymous)
    user_id INT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,

    -- Contact information
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,

    -- Multiple choice responses
    biggest_hurdle ENUM('A', 'B', 'C', 'D', 'E') NOT NULL,
    biggest_hurdle_other TEXT NULL,
    primary_motivation ENUM('A', 'B', 'C', 'D') NULL,
    time_consuming_part ENUM('A', 'B', 'C', 'D') NULL,
    professional_fear ENUM('A', 'B', 'C', 'D') NOT NULL,

    -- Short answer responses (2-4 sentences each)
    monetization_considerations TEXT NULL,
    professional_legacy TEXT NULL,
    platform_impact TEXT NOT NULL,

    -- Metadata
    submitted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,

    -- Performance indexes
    INDEX idx_feedback_user_id (user_id),
    INDEX idx_feedback_submitted_at (submitted_at),
    INDEX idx_feedback_biggest_hurdle (biggest_hurdle),
    INDEX idx_feedback_primary_motivation (primary_motivation),
    INDEX idx_feedback_professional_fear (professional_fear),
    INDEX idx_feedback_time_consuming_part (time_consuming_part)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SAMPLE DATA
-- =====================================================
-- Password for all users is 'password123' (hashed with bcrypt)
INSERT INTO users (name, email, password_hash, is_active, is_admin) VALUES
('John Doe', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.i8eO', TRUE, FALSE),
('Jane Smith', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.i8eO', TRUE, FALSE),
('Admin User', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.i8eO', TRUE, TRUE),
('Mike Johnson', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.i8eO', TRUE, FALSE),
('Sarah Wilson', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.i8eO', TRUE, FALSE);

-- Initialize user_stats table with current counts
INSERT INTO user_stats (total_users, total_active_users, total_admin_users)
SELECT
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as total_active_users,
    COUNT(CASE WHEN is_admin = TRUE THEN 1 END) as total_admin_users
FROM users;

-- =====================================================
-- STORED PROCEDURES - Ultra-Fast Performance Edition
-- =====================================================
DELIMITER //

-- Ultra-fast user lookup by email
DROP PROCEDURE IF EXISTS GetUserByEmailFast//
CREATE PROCEDURE GetUserByEmailFast(IN user_email VARCHAR(255))
BEGIN
    SELECT id, name, email, password_hash, total_points, shares_count,
           default_rank, current_rank, is_admin, is_active, created_at, updated_at
    FROM users
    WHERE email = user_email AND is_active = TRUE
    LIMIT 1;
END//

-- Ultra-fast user lookup by ID
DROP PROCEDURE IF EXISTS GetUserByIdFast//
CREATE PROCEDURE GetUserByIdFast(IN user_id INT)
BEGIN
    SELECT id, name, email, password_hash, total_points, shares_count,
           default_rank, current_rank, is_admin, is_active, created_at, updated_at
    FROM users
    WHERE id = user_id AND is_active = TRUE
    LIMIT 1;
END//

-- Ultra-fast user statistics
DROP PROCEDURE IF EXISTS GetUserStatsFast//
CREATE PROCEDURE GetUserStatsFast()
BEGIN
    SELECT
        total_users,
        total_active_users,
        total_admin_users,
        (total_users - total_admin_users) as non_admin_users,
        (total_active_users - total_admin_users) as active_non_admin_users,
        last_updated
    FROM user_stats
    LIMIT 1;
END//

-- Get user count efficiently
DROP PROCEDURE IF EXISTS GetUserCount//
CREATE PROCEDURE GetUserCount()
BEGIN
    SELECT total_users FROM user_stats LIMIT 1;
END//

-- Get non-admin user count efficiently
DROP PROCEDURE IF EXISTS GetNonAdminUserCount//
CREATE PROCEDURE GetNonAdminUserCount()
BEGIN
    SELECT (total_users - total_admin_users) as non_admin_users FROM user_stats LIMIT 1;
END//

-- Enhanced user stats update
DROP PROCEDURE IF EXISTS sp_UpdateUserStats//
CREATE PROCEDURE sp_UpdateUserStats(IN p_user_id INT, IN p_points_to_add INT)
BEGIN
    UPDATE users
    SET total_points = total_points + p_points_to_add,
        shares_count = shares_count + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_user_id;
END//

-- Ultra-fast user ranking calculation
DROP PROCEDURE IF EXISTS GetUserRankingFast//
CREATE PROCEDURE GetUserRankingFast(IN user_id INT)
BEGIN
    SELECT
        u.id,
        u.total_points,
        u.default_rank,
        u.current_rank,
        (SELECT COUNT(*) + 1
         FROM users u2
         WHERE u2.is_admin = FALSE
         AND u2.total_points > u.total_points) as calculated_rank,
        (SELECT COUNT(*)
         FROM users u3
         WHERE u3.is_admin = FALSE) as total_users
    FROM users u
    WHERE u.id = user_id AND u.is_admin = FALSE
    LIMIT 1;
END//

-- Ultra-fast leaderboard query
DROP PROCEDURE IF EXISTS GetLeaderboardFast//
CREATE PROCEDURE GetLeaderboardFast(IN page_offset INT, IN page_limit INT)
BEGIN
    SELECT
        u.id,
        u.name,
        u.total_points,
        u.shares_count,
        u.default_rank,
        u.current_rank,
        ROW_NUMBER() OVER (ORDER BY u.total_points DESC, u.created_at ASC) as rank_position
    FROM users u
    WHERE u.is_admin = FALSE AND u.is_active = TRUE
    ORDER BY u.total_points DESC, u.created_at ASC
    LIMIT page_limit OFFSET page_offset;
END//

DROP PROCEDURE IF EXISTS sp_GetUserRank//
CREATE PROCEDURE sp_GetUserRank(IN p_user_id INT)
BEGIN
    SELECT rank_info.*
    FROM (
        SELECT
            u.id,
            u.name,
            u.total_points,
            ROW_NUMBER() OVER (ORDER BY total_points DESC) AS user_rank
        FROM users u
        WHERE u.is_admin = FALSE
    ) AS rank_info
    WHERE rank_info.id = p_user_id;
END//

DROP PROCEDURE IF EXISTS sp_GetLeaderboard//
CREATE PROCEDURE sp_GetLeaderboard(IN p_page INT, IN p_limit INT)
BEGIN
    DECLARE v_offset INT;
    IF p_page < 1 THEN SET p_page = 1; END IF;
    IF p_limit < 1 THEN SET p_limit = 10; END IF;
    SET v_offset = (p_page - 1) * p_limit;
    SELECT
        u.id,
        u.name,
        u.email,
        u.total_points,
        u.shares_count,
        ROW_NUMBER() OVER (ORDER BY u.total_points DESC) as user_rank
    FROM users u
    WHERE u.is_admin = FALSE
    ORDER BY u.total_points DESC
    LIMIT p_limit OFFSET v_offset;
END//

DELIMITER ;

-- =====================================================
-- TRIGGERS - Automatic Statistics Maintenance
-- =====================================================
DELIMITER //

-- Trigger for user insertion - maintains user_stats automatically
DROP TRIGGER IF EXISTS update_user_stats_on_insert//
CREATE TRIGGER update_user_stats_on_insert
AFTER INSERT ON users
FOR EACH ROW
BEGIN
    UPDATE user_stats
    SET
        total_users = total_users + 1,
        total_active_users = total_active_users + CASE WHEN NEW.is_active = TRUE THEN 1 ELSE 0 END,
        total_admin_users = total_admin_users + CASE WHEN NEW.is_admin = TRUE THEN 1 ELSE 0 END,
        last_updated = CURRENT_TIMESTAMP;
END//

-- Trigger for user updates - maintains user_stats automatically
DROP TRIGGER IF EXISTS update_user_stats_on_update//
CREATE TRIGGER update_user_stats_on_update
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    DECLARE active_change INT DEFAULT 0;
    DECLARE admin_change INT DEFAULT 0;

    -- Calculate changes in active status
    IF OLD.is_active != NEW.is_active THEN
        SET active_change = CASE WHEN NEW.is_active = TRUE THEN 1 ELSE -1 END;
    END IF;

    -- Calculate changes in admin status
    IF OLD.is_admin != NEW.is_admin THEN
        SET admin_change = CASE WHEN NEW.is_admin = TRUE THEN 1 ELSE -1 END;
    END IF;

    -- Update stats if there are changes
    IF active_change != 0 OR admin_change != 0 THEN
        UPDATE user_stats
        SET
            total_active_users = total_active_users + active_change,
            total_admin_users = total_admin_users + admin_change,
            last_updated = CURRENT_TIMESTAMP;
    END IF;
END//

-- Trigger for user deletion - maintains user_stats automatically
DROP TRIGGER IF EXISTS update_user_stats_on_delete//
CREATE TRIGGER update_user_stats_on_delete
AFTER DELETE ON users
FOR EACH ROW
BEGIN
    UPDATE user_stats
    SET
        total_users = total_users - 1,
        total_active_users = total_active_users - CASE WHEN OLD.is_active = TRUE THEN 1 ELSE 0 END,
        total_admin_users = total_admin_users - CASE WHEN OLD.is_admin = TRUE THEN 1 ELSE 0 END,
        last_updated = CURRENT_TIMESTAMP;
END//

DROP TRIGGER IF EXISTS trg_after_share_event_insert//
CREATE TRIGGER trg_after_share_event_insert
AFTER INSERT ON share_events
FOR EACH ROW
BEGIN
    CALL sp_UpdateUserStats(NEW.user_id, NEW.points_earned);
END//

DELIMITER ;

-- These inserts will now fire the trigger correctly.
-- Using current point values from share_service.py: twitter=25, instagram=30, linkedin=50, facebook=35
INSERT INTO share_events (user_id, platform, points_earned) VALUES
(1, 'twitter', 25), (1, 'facebook', 35), (1, 'linkedin', 50),
(2, 'instagram', 30), (2, 'twitter', 25),
(4, 'facebook', 35), (4, 'linkedin', 50), (4, 'instagram', 30), (4, 'twitter', 25),
(5, 'facebook', 35), (5, 'linkedin', 50);

-- =====================================================
-- VIEWS - Performance Optimized
-- =====================================================

-- Ultra-fast leaderboard view
DROP VIEW IF EXISTS v_leaderboard_fast;
CREATE VIEW v_leaderboard_fast AS
SELECT
    u.id,
    u.name,
    u.total_points,
    u.shares_count,
    u.default_rank,
    u.current_rank,
    ROW_NUMBER() OVER (ORDER BY u.total_points DESC, u.created_at ASC) as rank_position,
    CASE
        WHEN u.default_rank IS NOT NULL AND u.current_rank IS NOT NULL
        THEN u.default_rank - u.current_rank
        ELSE 0
    END as rank_improvement
FROM users u
WHERE u.is_admin = FALSE AND u.is_active = TRUE
ORDER BY u.total_points DESC, u.created_at ASC;

-- Enhanced user stats view
DROP VIEW IF EXISTS view_user_stats;
CREATE VIEW view_user_stats AS
SELECT
    u.id,
    u.name,
    u.email,
    u.total_points,
    u.shares_count,
    u.default_rank,
    u.current_rank,
    u.is_admin,
    u.is_active,
    u.created_at,
    COUNT(se.id) as total_share_events,
    MAX(se.created_at) as last_share_date,
    COALESCE(SUM(se.points_earned), 0) as calculated_points
FROM users u
LEFT JOIN share_events se ON u.id = se.user_id
GROUP BY u.id, u.name, u.email, u.total_points, u.shares_count,
         u.default_rank, u.current_rank, u.is_admin, u.is_active, u.created_at;

-- Enhanced platform stats view
DROP VIEW IF EXISTS view_platform_stats;
CREATE VIEW view_platform_stats AS
SELECT
    se.platform,
    COUNT(*) as total_shares,
    SUM(se.points_earned) as total_points,
    COUNT(DISTINCT se.user_id) as unique_users,
    AVG(se.points_earned) as avg_points_per_share,
    MIN(se.created_at) as first_share_date,
    MAX(se.created_at) as last_share_date
FROM share_events se
GROUP BY se.platform;

-- =====================================================
-- PERFORMANCE OPTIMIZATION COMMANDS
-- =====================================================

-- Analyze tables for optimal query planning
ANALYZE TABLE users;
ANALYZE TABLE share_events;
ANALYZE TABLE email_queue;
ANALYZE TABLE feedback;
ANALYZE TABLE user_stats;

-- Optimize table statistics
OPTIMIZE TABLE users;
OPTIMIZE TABLE share_events;
OPTIMIZE TABLE email_queue;
OPTIMIZE TABLE feedback;
OPTIMIZE TABLE user_stats;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify indexes were created
SELECT
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    INDEX_TYPE,
    NON_UNIQUE
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('users', 'share_events', 'email_queue', 'feedback', 'user_stats')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- Verify user_stats initialization
SELECT
    'User Statistics Initialized' as status,
    total_users,
    total_active_users,
    total_admin_users,
    (total_users - total_admin_users) as non_admin_users,
    last_updated
FROM user_stats;

-- Verify sample data
SELECT
    'Sample Data Loaded' as status,
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_admin = TRUE THEN 1 END) as admin_users,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_users
FROM users;

-- =====================================================
-- SCRIPT EXECUTION COMPLETE - ULTRA PERFORMANCE EDITION
-- Database Schema Version: 2.0
-- Features: Sub-second performance, Redis caching support,
--          Automatic statistics maintenance, Ultra-fast procedures
-- =====================================================

SELECT
    'LawVriksh Database Schema 2.0 - Ultra Performance Edition' as message,
    'Schema created successfully with sub-second optimizations' as status,
    NOW() as completed_at;
